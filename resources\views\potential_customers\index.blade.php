@extends('layouts.index')

@section('content')
<!-- Content Header (Page header) -->
<section class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1>Potansi<PERSON>l Müşteri Listesi</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Ana Sayfa</a></li>
                    <li class="breadcrumb-item active">Potansiyel Müşteri Listesi</li>
                </ol>
            </div>
        </div>
    </div><!-- /.container-fluid -->
</section>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <a href="{{ route('potential-customers.create') }}" class="btn btn-primary">Ye<PERSON> Potansiyel Müşteri</a>
                        </h3>

                        <div class="card-tools">
                            @if(session('success'))
                                <div class="alert alert-success">
                                    {{ session('success') }}
                                </div>
                            @endif


                        </div>
                    </div>
                    <!-- /.card-header -->

                    <div class="card-body table-responsive p-0" style="height: 100%;">
                        <!-- Export Buttons -->
                        <div class="mb-3 text-right" style="padding: 15px;">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-success btn-sm" id="exportExcelPotential">
                                    <i class="fas fa-file-excel"></i> Excel
                                </button>
                                <button type="button" class="btn btn-info btn-sm" onclick="window.print()">
                                    <i class="fas fa-print"></i> Yazdır
                                </button>
                            </div>
                        </div>

                        <table id="potentialCustomersTable" class="table table-head-fixed text-nowrap table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Firma Adı</th>
                                    <th>Yetkili İsim</th>
                                    <th>Yetkili Soyisim</th>
                                    <th>Şirket Telefonları</th>
                                    <th>Şehir</th>
                                    <th>İlçe</th>
                                    <th>Teklif Durumu</th>
                                    <th>Teklif Tarihi</th>
                                    <th>İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Server-side DataTable ile doldurulacak -->
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    var table = $('#potentialCustomersTable').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "{{ route('potential-customers.datatable') }}",
            "type": "GET"
        },
        "language": {
            "url": "https://cdn.datatables.net/plug-ins/1.13.7/i18n/tr.json"
        },
        "pageLength": 25,
        "lengthMenu": [[10, 25, 50, 100], [10, 25, 50, 100]],
        "order": [[0, "desc"]],
        "responsive": true,
        "autoWidth": false,
        "columns": [
            { "data": 0, "name": "id" },
            { "data": 1, "name": "company_name" },
            { "data": 2, "name": "authorized_name" },
            { "data": 3, "name": "authorized_lastname" },
            { "data": 4, "name": "phones", "orderable": false },
            { "data": 5, "name": "city" },
            { "data": 6, "name": "district" },
            { "data": 7, "name": "offer_status" },
            { "data": 8, "name": "offer_date" },
            { "data": 9, "name": "actions", "orderable": false, "searchable": false }
        ],
        "initComplete": function () {
            // Her sütun için filtreleme input'u ekle
            this.api().columns().every(function (index) {
                var column = this;
                var title = $(column.header()).text();

                // İşlemler sütunu için filtreleme ekleme
                if (index === $('#potentialCustomersTable thead th').length - 1) {
                    return;
                }

                // Header'a filtreleme input'u ekle
                var input = $('<input type="text" placeholder="' + title + ' ara..." class="form-control form-control-sm" style="margin-top: 5px;" />')
                    .appendTo($(column.header()))
                    .on('keyup change clear', function () {
                        if (column.search() !== this.value) {
                            column.search(this.value).draw();
                        }
                    });
            });
        }
    });

    // Export button handlers
    $('#exportExcelPotential').on('click', function() {
        exportDataPotential('excel');
    });

    function exportDataPotential(type) {
        // Mevcut arama ve filtre parametrelerini al
        var searchValue = table.search();
        var columns = [];

        // Her sütun için filtre değerlerini al
        table.columns().every(function(index) {
            var column = this;
            var input = $(column.header()).find('input');
            var searchValue = input.length ? input.val() : '';
            columns.push({
                search: {
                    value: searchValue
                }
            });
        });

        // Form oluştur ve parametreleri ekle
        var form = $('<form>', {
            method: 'GET',
            action: type === 'excel' ? '{{ route("potential-customers.export.excel") }}' : ''
        });

        // Genel arama parametresi
        if (searchValue) {
            form.append($('<input>', {
                type: 'hidden',
                name: 'search',
                value: searchValue
            }));
        }

        // Sütun filtre parametreleri
        columns.forEach(function(column, index) {
            if (column.search.value) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'columns[' + index + '][search][value]',
                    value: column.search.value
                }));
            }
        });

        // Formu body'ye ekle, submit et ve kaldır
        $('body').append(form);
        form.submit();
        form.remove();
    }
});
</script>
@endpush


