@extends('layouts.index')

@section('title', '<PERSON><PERSON>')

@section('content')
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0"><PERSON><PERSON></h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Ana Sayfa</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('dealers.index') }}">Bayi Yönetimi</a></li>
                    <li class="breadcrumb-item active"><PERSON>i Detayı</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">{{ $dealer->name }}</h3>
                        <div class="card-tools">
                            @if($dealer->status)
                                <span class="badge badge-success">Aktif</span>
                            @else
                                <span class="badge badge-danger">Pasif</span>
                            @endif
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label>Bayi Adı:</label>
                                    <p>{{ $dealer->name }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label>Bölge:</label>
                                    <p>{{ $dealer->region->name ?? '-' }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label>İletişim Kişisi:</label>
                                    <p>{{ $dealer->contact_person ?? '-' }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label>Telefon:</label>
                                    <p>{{ $dealer->phone ?? '-' }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label>E-posta:</label>
                                    <p>{{ $dealer->email ?? '-' }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label>Durum:</label>
                                    <p>
                                        @if($dealer->status)
                                            <span class="badge badge-success">Aktif</span>
                                        @else
                                            <span class="badge badge-danger">Pasif</span>
                                        @endif
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label>Şehir:</label>
                                    <p>{{ $dealer->city ?? '-' }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label>İlçe:</label>
                                    <p>{{ $dealer->district ?? '-' }}</p>
                                </div>
                            </div>
                        </div>

                        @if($dealer->address)
                        <div class="row">
                            <div class="col-12">
                                <div class="info-group">
                                    <label>Adres:</label>
                                    <p>{{ $dealer->address }}</p>
                                </div>
                            </div>
                        </div>
                        @endif

                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label>Oluşturulma Tarihi:</label>
                                    <p>{{ $dealer->created_at->format('d.m.Y H:i') }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label>Son Güncelleme:</label>
                                    <p>{{ $dealer->updated_at->format('d.m.Y H:i') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex gap-2">
                        <a href="{{ route('dealers.edit', $dealer->id) }}" class="btn btn-primary flex-fill">Düzenle</a>
                        <a href="{{ route('dealers.index') }}" class="btn btn-secondary flex-fill">Geri Dön</a>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Müşteriler ({{ $dealer->customers->count() }})</h3>
                    </div>
                    <div class="card-body">
                        @forelse($dealer->customers as $customer)
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <strong>{{ $customer->company_name ?? $customer->authorized_first_name . ' ' . $customer->authorized_last_name }}</strong><br>
                                    <small class="text-muted">{{ $customer->email }}</small>
                                </div>
                                <div>
                                    <a href="{{ route('customers.show', $customer->id) }}" class="btn btn-sm btn-info">Detay</a>
                                </div>
                            </div>
                            <hr>
                        @empty
                            <p class="text-muted">Bu bayiye henüz müşteri atanmamış.</p>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.info-group {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #007bff;
    margin-bottom: 10px;
}

.info-group label {
    font-size: 0.9rem;
    margin-bottom: 5px;
    display: block;
}

.info-group p {
    font-size: 1rem;
    margin: 0;
    color: #333;
}

.badge {
    font-size: 0.8rem;
    padding: 6px 12px;
}
</style>
@endsection
