<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\RoleMigrationService;
use Exception;

class BackupRoles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backup:roles 
                            {--list : Mevcut yedekleri listele}
                            {--clean=5 : Eski yedekleri temizle (kaç tane tutulacak)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Roles verilerini yedekle ve yedek yönetimi yap';

    private RoleMigrationService $migrationService;

    public function __construct(RoleMigrationService $migrationService)
    {
        parent::__construct();
        $this->migrationService = $migrationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            if ($this->option('list')) {
                $this->listBackups();
                return 0;
            }

            if ($this->option('clean')) {
                $keepCount = (int) $this->option('clean');
                $this->cleanBackups($keepCount);
                return 0;
            }

            // Yedek oluştur
            $this->info('💾 Roles verileri yedekleniyor...');
            $backupFile = $this->migrationService->backupCurrentRoles();
            $this->info("✅ Yedek başarıyla oluşturuldu: {$backupFile}");

            // İstatistikleri göster
            $this->showBackupStats();

        } catch (Exception $e) {
            $this->error('❌ Hata: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * Mevcut yedekleri listele
     */
    private function listBackups()
    {
        $backups = $this->migrationService->listBackups();

        if (empty($backups)) {
            $this->warn('⚠️  Hiç yedek dosyası bulunamadı.');
            return;
        }

        $this->info("📋 Toplam {" . count($backups) . "} yedek dosyası bulundu:");

        $tableData = [];
        foreach ($backups as $backup) {
            $tableData[] = [
                'Dosya' => basename($backup['file']),
                'Tarih' => $backup['timestamp'],
                'Role Sayısı' => $backup['total_roles'],
                'Boyut' => $this->formatBytes($backup['size']),
                'Oluşturulma' => date('d.m.Y H:i', $backup['created_at']),
            ];
        }

        $this->table(['Dosya', 'Tarih', 'Role Sayısı', 'Boyut', 'Oluşturulma'], $tableData);
    }

    /**
     * Eski yedekleri temizle
     */
    private function cleanBackups(int $keepCount)
    {
        $this->info("🧹 Eski yedekler temizleniyor (son {$keepCount} yedek korunacak)...");
        
        $deletedCount = $this->migrationService->cleanOldBackups($keepCount);
        
        if ($deletedCount > 0) {
            $this->info("✅ {$deletedCount} eski yedek dosyası silindi.");
        } else {
            $this->info('ℹ️  Silinecek eski yedek bulunamadı.');
        }
    }

    /**
     * Yedek istatistiklerini göster
     */
    private function showBackupStats()
    {
        $stats = $this->migrationService->getMigrationStats();
        
        $this->info('📊 Yedek İstatistikleri:');
        $this->table(['Bilgi', 'Değer'], [
            ['Toplam Yedek Sayısı', $stats['available_backups']],
            ['Son Yedek Tarihi', $stats['last_backup']['timestamp'] ?? 'Yok'],
            ['Yedeklenen Role Sayısı', $stats['last_backup']['total_roles'] ?? 'Yok'],
        ]);
    }

    /**
     * Byte'ları okunabilir formata çevir
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
