@extends('layouts.index')

@section('content')
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card card-primary">
                <div class="card-header">
                    <h3 class="card-title text-center fw-bold py-2">Müşteri Detayı</h3>
                </div>
                <div class="card-body">
                    
                    <!-- Kimlik Bilgileri -->
                    <!--<h5 class="mb-3 text-primary">Kimlik Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">PlusCard No:</label>
                                <p class="mb-2">{{ $customer->pluscard_no ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                    </div> -->
                    

                    <!-- <PERSON>rma Bilgileri -->
                    <h5 class="mb-3 text-primary">Firma Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Firma Adı:</label>
                                <p class="mb-2">{{ $customer->company_name ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                    
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">E-posta:</label>
                                <p class="mb-2">{{ $customer->email ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Web Sitesi:</label>
                                <p class="mb-2">{{ $customer->website_url ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Google Haritalar</label>
                                <p class="mb-2">{{ $customer->google_maps_url ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="fw-bold text-muted">İlan Sitesi</label>
                                <p class="mb-2">{{ $customer->listing_url ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Yetkili Kişi Bilgileri -->
                    <h5 class="mb-3 text-primary">Yetkili Kişi Bilgileri</h5>

                    @php
                        $allAuthorizedPersons = collect();

                        // Eski tek yetkili kişi bilgilerini ekle (eğer varsa)
                        if($customer->authorized_title || $customer->authorized_first_name || $customer->authorized_last_name || $customer->authorized_phone) {
                            $allAuthorizedPersons->push((object)[
                                'title' => $customer->authorized_title,
                                'first_name' => $customer->authorized_first_name,
                                'last_name' => $customer->authorized_last_name,
                                'phone' => $customer->authorized_phone,
                            ]);
                        }

                        // Yeni yetkili kişileri ekle
                        if($customer->authorizedPersons) {
                            $allAuthorizedPersons = $allAuthorizedPersons->merge($customer->authorizedPersons);
                        }
                    @endphp

                    @if($allAuthorizedPersons->count() > 0)
                        @foreach($allAuthorizedPersons as $index => $person)
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-secondary mb-3">Yetkili Kişi #{{ $index + 1 }}</h6>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <label class="fw-bold text-muted">Yetkili Ünvan:</label>
                                        <p class="mb-2">{{ $person->title ?? 'Belirtilmemiş' }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <label class="fw-bold text-muted">Yetkili Telefon:</label>
                                        <p class="mb-2">{{ $person->phone ?? 'Belirtilmemiş' }}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <label class="fw-bold text-muted">Yetkili İsim:</label>
                                        <p class="mb-2">{{ $person->first_name ?? 'Belirtilmemiş' }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <label class="fw-bold text-muted">Yetkili Soyisim:</label>
                                        <p class="mb-2">{{ $person->last_name ?? 'Belirtilmemiş' }}</p>
                                    </div>
                                </div>
                            </div>
                            @if(!$loop->last)
                                <hr class="my-4">
                            @endif
                        @endforeach
                    @else
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="info-group">
                                    <p class="mb-2 text-muted">Yetkili kişi bilgisi bulunmamaktadır.</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- İletişim Bilgileri -->
                    <h5 class="mb-3 text-primary">İletişim Bilgileri</h5>

                    <!-- Şirket Telefonları -->
                    <div class="mb-4">
                        <h6 class="mb-3 text-secondary">Şirket Telefonları</h6>
                        @php
                            $allPhones = collect();

                            if(method_exists($customer, 'phones') && $customer->phones) {
                                $newPhones = $customer->phones;
                                foreach($newPhones as $phone) {
                                    $allPhones->push($phone);
                                }
                            }
                        @endphp

                        @if($allPhones->count() > 0)
                            <div class="row">
                                @foreach($allPhones as $index => $phone)
                                    <div class="col-md-6 mb-3">
                                        <div class="card border-start border-primary border-3 h-100 shadow-sm">
                                            <div class="card-body py-3">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div class="flex-grow-1">
                                                        <h6 class="text-primary mb-2">
                                                            <i class="fas fa-phone me-2"></i>Telefon #{{ $index + 1 }}
                                                        </h6>
                                                        <p class="mb-1 fw-bold fs-6">
                                                            <a href="tel:{{ $phone->phone }}" class="text-decoration-none text-dark">
                                                                {{ $phone->phone }}
                                                            </a>
                                                        </p>
                                                        <small class="text-muted">
                                                            <i class="fas fa-tag me-1"></i>{{ $phone->type }}
                                                        </small>
                                                    </div>
                                                    <div class="text-end">
                                                        <span class="badge bg-{{ $phone->type == 'Mobil' ? 'success' : ($phone->type == 'Fax' ? 'warning' : ($phone->type == 'Diğer' ? 'info' : 'primary')) }} mb-2">
                                                            {{ $phone->type }}
                                                        </span>
                                                        <br>
                                                        <a href="tel:{{ $phone->phone }}" class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-phone"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @if(($index + 1) % 2 == 0)
                                        </div><div class="row">
                                    @endif
                                @endforeach
                            </div>
                        @else
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                Henüz şirket telefonu eklenmemiş.
                            </div>
                        @endif
                    </div>

                    <!-- Adres Bilgileri -->
                    <h5 class="mb-3 text-primary">Adres Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">İl:</label>
                                <p class="mb-2">{{ $customer->city ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">İlçe:</label>
                                <p class="mb-2">{{ $customer->district ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Adres:</label>
                                <p class="mb-2">{{ $customer->address ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="card-footer d-flex gap-2">
                    <a href="{{ route('customers.edit', $customer->id) }}" class="btn btn-primary flex-fill">Düzenle</a>
                    <a href="{{ route('customers.customer-followups.create', $customer->id) }}" class="btn btn-success flex-fill">Takip Ekle</a>
                    <a href="{{ route('customers.customer-branches.index', $customer->id) }}" class="btn btn-info flex-fill">Şubeleri Görüntüle</a>
                    <a href="{{ route('customers.index') }}" class="btn btn-secondary flex-fill">Geri Dön</a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.info-group {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #007bff;
    margin-bottom: 10px;
}

.info-group label {
    font-size: 0.9rem;
    margin-bottom: 5px;
    display: block;
}

.info-group p {
    font-size: 1rem;
    margin: 0;
    color: #333;
}

.badge {
    font-size: 0.8rem;
    padding: 6px 12px;
}
</style>
@endsection 