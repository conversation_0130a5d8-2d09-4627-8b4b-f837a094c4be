<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // \App\Models\User::factory(10)->create();
        DB::statement('ALTER TABLE dealers DROP CONSTRAINT IF EXISTS users_dealer_id_foreign');
        DB::statement('ALTER TABLE users DROP CONSTRAINT IF EXISTS users_dealer_id_foreign');

        $this->call(RoleSeeder::class);
        $this->call(CustomerSeeder::class);
        $this->call(CustomerFollowupSeeder::class);
        $this->call(PotentialCustomerSeeder::class);
        //$this->call(RegionSeeder::class);
        //$this->call(DealerSeeder::class);
        
        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => '+90 ************',
            'password' => Hash::make('test1234'),
        ]);

        try {
            DB::beginTransaction();

            $this->command->info('Databases truncated!');

            $sqlPath = database_path('seeders/adminuser.sql');

            if (File::exists($sqlPath)) {
                $sql = File::get($sqlPath);

                DB::unprepared($sql);
                DB::commit();

                $this->command->info('sample_data.sql successfully seeded!');
            } else {
                throw new \Exception('sample_data.sql not found!');
            }
        } catch (\Exception $e) {
            DB::rollBack();
            $this->command->error('Seeder hatası: ' . $e->getMessage());
        }
    }
}
