@extends('layouts.index')

@section('content')
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card card-primary">
                <div class="card-header">
                    <h3 class="card-title text-center fw-bold py-2">Potansiyel Müşteri Düzenle</h3>
                </div>
                <div class="card-body">
                    <form action="{{ route('potential-customers.update', $potentialCustomer->id) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <!-- Firma Bilgileri -->
                        <h5 class="mb-3 text-primary">Firma Bilgileri</h5>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="company_name" class="form-label">Firma Adı <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="company_name" name="company_name" value="{{ old('company_name', $potentialCustomer->company_name) }}" required>
                                    @error('company_name')<div class="text-danger">{{ $message }}</div>@enderror
                                </div>
                            </div>
                        </div>

                        <!-- Yetkili Kişi Bilgileri -->
                        <h5 class="mb-3 text-primary mt-4">Yetkili Kişi Bilgileri</h5>
                        <div id="authorized-persons-container">
                            @if($potentialCustomer->authorizedPersons && $potentialCustomer->authorizedPersons->count() > 0)
                                @foreach($potentialCustomer->authorizedPersons as $index => $person)
                                    <div class="authorized-person-item border p-3 mb-3 rounded" data-index="{{ $index }}">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h6 class="mb-0">Yetkili Kişi #{{ $index + 1 }}</h6>
                                            <button type="button" class="btn btn-sm btn-danger remove-authorized-person" {{ $loop->first && $loop->count == 1 ? 'style=display:none;' : '' }}>
                                                <i class="fas fa-trash"></i> Kaldır
                                            </button>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label class="form-label">Yetkili Ünvan</label>
                                                    <input type="text" class="form-control" name="authorized_persons[{{ $index }}][title]" value="{{ old('authorized_persons.'.$index.'.title', $person->title) }}" placeholder="Örn: Genel Müdür, Satış Müdürü">
                                                    @error('authorized_persons.'.$index.'.title')<div class="text-danger">{{ $message }}</div>@enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label class="form-label">Yetkili Telefon</label>
                                                    <input type="text" class="form-control inputmask" name="authorized_persons[{{ $index }}][phone]" value="{{ old('authorized_persons.'.$index.'.phone', $person->phone) }}" data-inputmask="'mask': '+99 999 999 99 99'" placeholder="+90 5xx xxx xx xx">
                                                    @error('authorized_persons.'.$index.'.phone')<div class="text-danger">{{ $message }}</div>@enderror
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label class="form-label">Yetkili İsim</label>
                                                    <input type="text" class="form-control" name="authorized_persons[{{ $index }}][name]" value="{{ old('authorized_persons.'.$index.'.name', $person->name) }}" placeholder="Ad">
                                                    @error('authorized_persons.'.$index.'.name')<div class="text-danger">{{ $message }}</div>@enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label class="form-label">Yetkili Soyisim</label>
                                                    <input type="text" class="form-control" name="authorized_persons[{{ $index }}][lastname]" value="{{ old('authorized_persons.'.$index.'.lastname', $person->lastname) }}" placeholder="Soyad">
                                                    @error('authorized_persons.'.$index.'.lastname')<div class="text-danger">{{ $message }}</div>@enderror
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <div class="authorized-person-item border p-3 mb-3 rounded" data-index="0">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="mb-0">Yetkili Kişi #1</h6>
                                        <button type="button" class="btn btn-sm btn-danger remove-authorized-person" style="display: none;">
                                            <i class="fas fa-trash"></i> Kaldır
                                        </button>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label class="form-label">Yetkili Ünvan</label>
                                                <input type="text" class="form-control" name="authorized_persons[0][title]" value="{{ old('authorized_persons.0.title') }}" placeholder="Örn: Genel Müdür, Satış Müdürü">
                                                @error('authorized_persons.0.title')<div class="text-danger">{{ $message }}</div>@enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label class="form-label">Yetkili Telefon</label>
                                                <input type="text" class="form-control inputmask" name="authorized_persons[0][phone]" value="{{ old('authorized_persons.0.phone') }}" data-inputmask="'mask': '+99 999 999 99 99'" placeholder="+90 5xx xxx xx xx">
                                                @error('authorized_persons.0.phone')<div class="text-danger">{{ $message }}</div>@enderror
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label class="form-label">Yetkili İsim</label>
                                                <input type="text" class="form-control" name="authorized_persons[0][name]" value="{{ old('authorized_persons.0.name') }}" placeholder="Ad">
                                                @error('authorized_persons.0.name')<div class="text-danger">{{ $message }}</div>@enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label class="form-label">Yetkili Soyisim</label>
                                                <input type="text" class="form-control" name="authorized_persons[0][lastname]" value="{{ old('authorized_persons.0.lastname') }}" placeholder="Soyad">
                                                @error('authorized_persons.0.lastname')<div class="text-danger">{{ $message }}</div>@enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                        <div class="mb-3">
                            <button type="button" id="add-authorized-person" class="btn btn-sm btn-success">
                                <i class="fas fa-plus"></i> Yeni Yetkili Ekle
                            </button>
                        </div>

                        <!-- Şirket Telefonları -->
                        <h5 class="mb-3 text-primary mt-4">Şirket Telefonları</h5>
                        <div id="company-phones-container">
                            @php
                                $allPhones = collect();

                                if(method_exists($potentialCustomer, 'phones') && $potentialCustomer->phones) {
                                    $allPhones = $potentialCustomer->phones;
                                }

                                $phoneIndex = 0;
                            @endphp

                            @if($allPhones->count() > 0)
                                @foreach($allPhones as $phone)
                                    <div class="company-phone-item border p-3 mb-3 rounded" data-index="{{ $phoneIndex }}">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h6 class="mb-0">Şirket Telefon #{{ $phoneIndex + 1 }}</h6>
                                            <button type="button" class="btn btn-sm btn-danger remove-company-phone" {{ $phoneIndex == 0 && $allPhones->count() == 1 ? 'style=display:none;' : '' }}>
                                                <i class="fas fa-trash"></i> Kaldır
                                            </button>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label class="form-label">Telefon Numarası</label>
                                                    <input type="text" class="form-control inputmask" name="company_phones[{{ $phoneIndex }}][phone]" value="{{ old('company_phones.'.$phoneIndex.'.phone', $phone->phone) }}" data-inputmask="'mask': '+99 999 999 99 99'" placeholder="+90 2xx xxx xx xx">
                                                    <input type="hidden" name="company_phones[{{ $phoneIndex }}][id]" value="{{ $phone->id }}">
                                                    @error('company_phones.'.$phoneIndex.'.phone')<div class="text-danger">{{ $message }}</div>@enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label class="form-label">Telefon Türü</label>
                                                    <select class="form-control" name="company_phones[{{ $phoneIndex }}][type]">
                                                        <option value="Sabit" {{ old('company_phones.'.$phoneIndex.'.type', $phone->type) == 'Sabit' ? 'selected' : '' }}>Sabit</option>
                                                        <option value="Mobil" {{ old('company_phones.'.$phoneIndex.'.type', $phone->type) == 'Mobil' ? 'selected' : '' }}>Mobil</option>
                                                        <option value="Fax" {{ old('company_phones.'.$phoneIndex.'.type', $phone->type) == 'Fax' ? 'selected' : '' }}>Fax</option>
                                                        <option value="Diğer" {{ old('company_phones.'.$phoneIndex.'.type', $phone->type) == 'Diğer' ? 'selected' : '' }}>Diğer</option>
                                                    </select>
                                                    @error('company_phones.'.$phoneIndex.'.type')<div class="text-danger">{{ $message }}</div>@enderror
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @php $phoneIndex++; @endphp
                                @endforeach
                            @else
                                <div class="company-phone-item border p-3 mb-3 rounded" data-index="0">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="mb-0">Şirket Telefon #1</h6>
                                        <button type="button" class="btn btn-sm btn-danger remove-company-phone" style="display: none;">
                                            <i class="fas fa-trash"></i> Kaldır
                                        </button>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label class="form-label">Telefon Numarası</label>
                                                <input type="text" class="form-control inputmask" name="company_phones[0][phone]" value="{{ old('company_phones.0.phone') }}" data-inputmask="'mask': '+99 999 999 99 99'" placeholder="+90 2xx xxx xx xx">
                                                @error('company_phones.0.phone')<div class="text-danger">{{ $message }}</div>@enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label class="form-label">Telefon Türü</label>
                                                <select class="form-control" name="company_phones[0][type]">
                                                    <option value="Sabit" {{ old('company_phones.0.type', 'Sabit') == 'Sabit' ? 'selected' : '' }}>Sabit</option>
                                                    <option value="Mobil" {{ old('company_phones.0.type') == 'Mobil' ? 'selected' : '' }}>Mobil</option>
                                                    <option value="Fax" {{ old('company_phones.0.type') == 'Fax' ? 'selected' : '' }}>Fax</option>
                                                    <option value="Diğer" {{ old('company_phones.0.type') == 'Diğer' ? 'selected' : '' }}>Diğer</option>
                                                </select>
                                                @error('company_phones.0.type')<div class="text-danger">{{ $message }}</div>@enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                        <div class="mb-3">
                            <button type="button" id="add-company-phone" class="btn btn-sm btn-success">
                                <i class="fas fa-plus"></i> Yeni Telefon Ekle
                            </button>
                        </div>

                        <!-- Adres Bilgileri -->
                        <h5 class="mb-3 text-primary mt-4">Adres Bilgileri</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="city" class="form-label">İl <span class="text-danger">*</span></label>
                                    <select class="form-control" id="city" name="city" required>
                                        <option value="">İl seçiniz</option>
                                    </select>
                                    @error('city')<div class="text-danger">{{ $message }}</div>@enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="district" class="form-label">İlçe <span class="text-danger">*</span></label>
                                    <select class="form-control" id="district" name="district" required disabled>
                                        <option value="">İlçe seçiniz</option>
                                    </select>
                                    @error('district')<div class="text-danger">{{ $message }}</div>@enderror
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="address" class="form-label">Adres <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="address" name="address" rows="3" required placeholder="Detaylı adres bilgisi">{{ old('address', $potentialCustomer->address) }}</textarea>
                                    @error('address')<div class="text-danger">{{ $message }}</div>@enderror
                                </div>
                            </div>
                        </div>

                        <!-- Teklif ve Durum Bilgileri -->
                        <h5 class="mb-3 text-primary mt-4">Teklif ve Durum Bilgileri</h5>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="offer_status" class="form-label">Teklif Durumu <span class="text-danger">*</span></label>
                                    <select class="form-control" id="offer_status" name="offer_status" required>
                                        @foreach(\App\Models\PotentialCustomer::$offerStatusOptions as $value => $label)
                                            <option value="{{ $value }}" {{ old('offer_status', $potentialCustomer->offer_status) == $value ? 'selected' : '' }}>{{ $label }}</option>
                                        @endforeach
                                    </select>
                                    @error('offer_status')<div class="text-danger">{{ $message }}</div>@enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="offer_date" class="form-label">Teklif Tarihi</label>
                                    <input type="date" class="form-control" id="offer_date" name="offer_date" value="{{ old('offer_date', $potentialCustomer->offer_date ? \Carbon\Carbon::parse($potentialCustomer->offer_date)->format('Y-m-d') : null) }}">
                                    @error('offer_date')<div class="text-danger">{{ $message }}</div>@enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="is_working_with_us" class="form-label">Bizimle Çalışıyor mu? <span class="text-danger">*</span></label>
                                    <select class="form-control" id="is_working_with_us" name="is_working_with_us" required>
                                        <option value="0" {{ old('is_working_with_us', $potentialCustomer->is_working_with_us) == '0' ? 'selected' : '' }}>Hayır</option>
                                        <option value="1" {{ old('is_working_with_us', $potentialCustomer->is_working_with_us) == '1' ? 'selected' : '' }}>Evet</option>
                                    </select>
                                    @error('is_working_with_us')<div class="text-danger">{{ $message }}</div>@enderror
                                </div>
                            </div>
                        </div>

                        <div class="card-footer d-flex gap-2">
                            <a href="{{ route('customers.createFromPotential', $potentialCustomer->id) }}" class="btn btn-primary flex-fill">Müşteriye Dönüştür</a>
                            <button type="submit" class="btn btn-success flex-fill">Güncelle</button>
                            <a href="{{ route('potential-customers.index') }}" class="btn btn-secondary flex-fill">İptal</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="/assets/plugins/inputmask/jquery.inputmask.bundle.js"></script>
<script>
    let cityDistricts = {};
    $(document).ready(function(){
        const citySelect = $('#city');
        const districtSelect = $('#district');
        fetch('/assets/turkiye-il-ilce.json')
            .then(response => response.json())
            .then(data => {
                cityDistricts = data;
                Object.keys(cityDistricts).forEach(function(city) {
                    citySelect.append(`<option value="${city}">${city}</option>`);
                });
                // Eski değerleri doldur
                const oldCity = "{{ old('city', $potentialCustomer->city) }}";
                const oldDistrict = "{{ old('district', $potentialCustomer->district) }}";
                if (oldCity) {
                    citySelect.val(oldCity).trigger('change');
                    if (oldDistrict) {
                        setTimeout(function() {
                            districtSelect.val(oldDistrict);
                        }, 100);
                    }
                }
            });
        citySelect.on('change', function() {
            const selectedCity = $(this).val();
            districtSelect.empty().append('<option value="">İlçe seçiniz</option>');
            if (selectedCity && cityDistricts[selectedCity]) {
                cityDistricts[selectedCity].forEach(function(district) {
                    districtSelect.append(`<option value="${district}">${district}</option>`);
                });
                districtSelect.prop('disabled', false);
            } else {
                districtSelect.prop('disabled', true);
            }
        });
        $(".inputmask").inputmask({
            mask: "+99 999 999 99 99",
            showMaskOnHover: false,
            showMaskOnFocus: true,
            clearIncomplete: true,
            definitions: {
                '9': {
                    validator: "[0-9]",
                    cardinality: 1,
                    definitionSymbol: "9"
                }
            },
            onBeforePaste: function (pastedValue, opts) {
                return pastedValue.replace(/[^\d\+]/g, '');
            },
            onKeyDown: function(e, buffer, caretPos, opts) {
                var key = e.key;
                if (!/[0-9]/.test(key) && key.length === 1) {
                    e.preventDefault();
                }
            }
        });
    });

    // Yetkili kişi ekleme/çıkarma işlemleri
    let authorizedPersonIndex = {{ $potentialCustomer->authorizedPersons ? $potentialCustomer->authorizedPersons->count() : 1 }};

    $('#add-authorized-person').on('click', function() {
        const container = $('#authorized-persons-container');
        const newItem = `
            <div class="authorized-person-item border p-3 mb-3 rounded" data-index="${authorizedPersonIndex}">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">Yetkili Kişi #${authorizedPersonIndex + 1}</h6>
                    <button type="button" class="btn btn-sm btn-danger remove-authorized-person">
                        <i class="fas fa-trash"></i> Kaldır
                    </button>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Yetkili Ünvan</label>
                            <input type="text" class="form-control" name="authorized_persons[${authorizedPersonIndex}][title]" placeholder="Örn: Genel Müdür, Satış Müdürü">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Yetkili Telefon</label>
                            <input type="text" class="form-control inputmask" name="authorized_persons[${authorizedPersonIndex}][phone]" data-inputmask="'mask': '+99 999 999 99 99'" placeholder="+90 5xx xxx xx xx">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Yetkili İsim</label>
                            <input type="text" class="form-control" name="authorized_persons[${authorizedPersonIndex}][name]" placeholder="Ad">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Yetkili Soyisim</label>
                            <input type="text" class="form-control" name="authorized_persons[${authorizedPersonIndex}][lastname]" placeholder="Soyad">
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        container.append(newItem);
        
        // Yeni eklenen telefon alanına inputmask uygula
        container.find('.inputmask').last().inputmask({
            mask: "+99 999 999 99 99",
            showMaskOnHover: false,
            showMaskOnFocus: true,
            clearIncomplete: true
        });
        
        authorizedPersonIndex++;
        updateRemoveButtons();
    });

    $(document).on('click', '.remove-authorized-person', function() {
        $(this).closest('.authorized-person-item').remove();
        updateRemoveButtons();
        updatePersonNumbers();
    });

    function updateRemoveButtons() {
        const items = $('.authorized-person-item');
        if (items.length > 1) {
            $('.remove-authorized-person').show();
        } else {
            $('.remove-authorized-person').hide();
        }
    }

    function updatePersonNumbers() {
        $('.authorized-person-item').each(function(index) {
            $(this).find('h6').text(`Yetkili Kişi #${index + 1}`);
        });
    }

    // Şirket telefonu ekleme/çıkarma işlemleri
    let companyPhoneIndex = {{ $allPhones->count() > 0 ? $allPhones->count() : 1 }};

    $('#add-company-phone').on('click', function() {
        const container = $('#company-phones-container');
        const newItem = `
            <div class="company-phone-item border p-3 mb-3 rounded" data-index="${companyPhoneIndex}">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">Şirket Telefon #${companyPhoneIndex + 1}</h6>
                    <button type="button" class="btn btn-sm btn-danger remove-company-phone">
                        <i class="fas fa-trash"></i> Kaldır
                    </button>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Telefon Numarası</label>
                            <input type="text" class="form-control inputmask" name="company_phones[${companyPhoneIndex}][phone]" data-inputmask="'mask': '+99 999 999 99 99'" placeholder="+90 2xx xxx xx xx">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Telefon Türü</label>
                            <select class="form-control" name="company_phones[${companyPhoneIndex}][type]">
                                <option value="Sabit">Sabit</option>
                                <option value="Mobil">Mobil</option>
                                <option value="Fax">Fax</option>
                                <option value="Diğer">Diğer</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        `;

        container.append(newItem);

        // Yeni eklenen telefon alanına inputmask uygula
        container.find('.inputmask').last().inputmask({
            mask: "+99 999 999 99 99",
            showMaskOnHover: false,
            showMaskOnFocus: true,
            clearIncomplete: true
        });

        companyPhoneIndex++;
        updatePhoneRemoveButtons();
    });

    $(document).on('click', '.remove-company-phone', function() {
        $(this).closest('.company-phone-item').remove();
        updatePhoneRemoveButtons();
        updatePhoneNumbers();
    });

    function updatePhoneRemoveButtons() {
        const items = $('.company-phone-item');
        if (items.length > 1) {
            $('.remove-company-phone').show();
        } else {
            $('.remove-company-phone').hide();
        }
    }

    function updatePhoneNumbers() {
        $('.company-phone-item').each(function(index) {
            $(this).find('h6').text(`Şirket Telefon #${index + 1}`);
        });
    }

    // Sayfa yüklendiğinde remove butonlarını kontrol et
    $(document).ready(function() {
        updateRemoveButtons();
        updatePhoneRemoveButtons();
    });
</script>
@endpush 



