!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t=t||self).Sweetalert2=e()}(this,function(){"use strict";function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function i(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}function a(t,e,n){return e&&i(t.prototype,e),n&&i(t,n),t}function c(){return(c=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t}).apply(this,arguments)}function s(t){return(s=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function u(t,e){return(u=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function l(t,e,n){return(l=function(){if("undefined"!=typeof Reflect&&Reflect.construct&&!Reflect.construct.sham){if("function"==typeof Proxy)return 1;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),1}catch(t){return}}}()?Reflect.construct:function(t,e,n){var o=[null];o.push.apply(o,e);var i=new(Function.bind.apply(t,o));return n&&u(i,n.prototype),i}).apply(null,arguments)}function d(t,e){return!e||"object"!=typeof e&&"function"!=typeof e?function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t):e}function p(t,e,n){return(p="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var o=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=s(t)););return t}(t,e);if(o){var i=Object.getOwnPropertyDescriptor(o,e);return i.get?i.get.call(n):i.value}})(t,e,n||t)}function f(e){return Object.keys(e).map(function(t){return e[t]})}function m(t){return Array.prototype.slice.call(t)}function v(t){console.error("".concat(H," ").concat(t))}function h(t,e){var n;n='"'.concat(t,'" is deprecated and will be removed in the next major release. Please use "').concat(e,'" instead.'),-1===N.indexOf(n)&&(N.push(n),D(n))}function g(t){return t&&Promise.resolve(t)===t}function b(t){return t instanceof Element||"object"===r(e=t)&&e.jquery;var e}function t(t){var e={};for(var n in t)e[t[n]]="swal2-"+t[n];return e}function y(t){var e=W();return e?e.querySelector(t):null}function e(t){return y(".".concat(t))}function n(){var t=K();return m(t.querySelectorAll(".".concat(F.icon)))}function w(){var t=n().filter(function(t){return pt(t)});return t.length?t[0]:null}function C(){return e(F.title)}function k(){return e(F.content)}function x(){return e(F.image)}function P(){return e(F["progress-steps"])}function A(){return e(F["validation-message"])}function B(){return y(".".concat(F.actions," .").concat(F.confirm))}function S(){return y(".".concat(F.actions," .").concat(F.cancel))}function E(){return e(F.actions)}function O(){return e(F.header)}function T(){return e(F.footer)}function L(){return e(F["timer-progress-bar"])}function q(){return e(F.close)}function I(){var t=m(K().querySelectorAll('[tabindex]:not([tabindex="-1"]):not([tabindex="0"])')).sort(function(t,e){return t=parseInt(t.getAttribute("tabindex")),(e=parseInt(e.getAttribute("tabindex")))<t?1:t<e?-1:0}),e=m(K().querySelectorAll('\n  a[href],\n  area[href],\n  input:not([disabled]),\n  select:not([disabled]),\n  textarea:not([disabled]),\n  button:not([disabled]),\n  iframe,\n  object,\n  embed,\n  [tabindex="0"],\n  [contenteditable],\n  audio[controls],\n  video[controls],\n  summary\n')).filter(function(t){return"-1"!==t.getAttribute("tabindex")});return function(t){for(var e=[],n=0;n<t.length;n++)-1===e.indexOf(t[n])&&e.push(t[n]);return e}(t.concat(e)).filter(function(t){return pt(t)})}function j(){return!Y()&&!document.body.classList.contains(F["no-backdrop"])}function M(e,t){if(e.textContent="",t){var n=(new DOMParser).parseFromString(t,"text/html");m(n.querySelector("head").childNodes).forEach(function(t){e.appendChild(t)}),m(n.querySelector("body").childNodes).forEach(function(t){e.appendChild(t)})}}function V(t,e){if(e){for(var n=e.split(/\s+/),o=0;o<n.length;o++)if(!t.classList.contains(n[o]))return;return 1}}function R(t,e,n){var o,i;if(i=e,m((o=t).classList).forEach(function(t){-1===f(F).indexOf(t)&&-1===f(z).indexOf(t)&&-1===f(i.showClass).indexOf(t)&&o.classList.remove(t)}),e.customClass&&e.customClass[n]){if("string"!=typeof e.customClass[n]&&!e.customClass[n].forEach)return D("Invalid type of customClass.".concat(n,'! Expected string or iterable object, got "').concat(r(e.customClass[n]),'"'));ut(t,e.customClass[n])}}var H="SweetAlert2:",D=function(t){console.warn("".concat(H," ").concat(t))},N=[],U=function(t){return"function"==typeof t?t():t},_=Object.freeze({cancel:"cancel",backdrop:"backdrop",close:"close",esc:"esc",timer:"timer"}),F=t(["container","shown","height-auto","iosfix","popup","modal","no-backdrop","no-transition","toast","toast-shown","toast-column","show","hide","close","title","header","content","html-container","actions","confirm","cancel","footer","icon","icon-content","image","input","file","range","select","radio","checkbox","label","textarea","inputerror","validation-message","progress-steps","active-progress-step","progress-step","progress-step-line","loading","styled","top","top-start","top-end","top-left","top-right","center","center-start","center-end","center-left","center-right","bottom","bottom-start","bottom-end","bottom-left","bottom-right","grow-row","grow-column","grow-fullscreen","rtl","timer-progress-bar","timer-progress-bar-container","scrollbar-measure","icon-success","icon-warning","icon-info","icon-question","icon-error"]),z=t(["success","warning","info","question","error"]),W=function(){return document.body.querySelector(".".concat(F.container))},K=function(){return e(F.popup)},Y=function(){return document.body.classList.contains(F["toast-shown"])},Z={previousBodyPadding:null};function Q(t,e){if(!e)return null;switch(e){case"select":case"textarea":case"file":return dt(t,F[e]);case"checkbox":return t.querySelector(".".concat(F.checkbox," input"));case"radio":return t.querySelector(".".concat(F.radio," input:checked"))||t.querySelector(".".concat(F.radio," input:first-child"));case"range":return t.querySelector(".".concat(F.range," input"));default:return dt(t,F.input)}}function $(t){if(t.focus(),"file"!==t.type){var e=t.value;t.value="",t.value=e}}function J(t,e,n){t&&e&&("string"==typeof e&&(e=e.split(/\s+/).filter(Boolean)),e.forEach(function(e){t.forEach?t.forEach(function(t){n?t.classList.add(e):t.classList.remove(e)}):n?t.classList.add(e):t.classList.remove(e)}))}function X(t,e,n){n||0===parseInt(n)?t.style[e]="number"==typeof n?"".concat(n,"px"):n:t.style.removeProperty(e)}function G(t,e){var n=1<arguments.length&&void 0!==e?e:"flex";t.style.opacity="",t.style.display=n}function tt(t){t.style.opacity="",t.style.display="none"}function et(t,e,n){e?G(t,n):tt(t)}function nt(t){var e=window.getComputedStyle(t),n=parseFloat(e.getPropertyValue("animation-duration")||"0"),o=parseFloat(e.getPropertyValue("transition-duration")||"0");return 0<n||0<o}function ot(t,e){var n=1<arguments.length&&void 0!==e&&e,o=L();pt(o)&&(n&&(o.style.transition="none",o.style.width="100%"),setTimeout(function(){o.style.transition="width ".concat(t/1e3,"s linear"),o.style.width="0%"},10))}function it(){return"undefined"==typeof window||"undefined"==typeof document}function rt(t){Ge.isVisible()&&st!==t.target.value&&Ge.resetValidationMessage(),st=t.target.value}function at(t,e){t instanceof HTMLElement?e.appendChild(t):"object"===r(t)?ht(t,e):t&&M(e,t)}function ct(t,e){var n=E(),o=B(),i=S();e.showConfirmButton||e.showCancelButton||tt(n),R(n,e,"actions"),bt(o,"confirm",e),bt(i,"cancel",e),e.buttonsStyling?function(t,e,n){ut([t,e],F.styled),n.confirmButtonColor&&(t.style.backgroundColor=n.confirmButtonColor);n.cancelButtonColor&&(e.style.backgroundColor=n.cancelButtonColor);var o=window.getComputedStyle(t).getPropertyValue("background-color");t.style.borderLeftColor=o,t.style.borderRightColor=o}(o,i,e):(lt([o,i],F.styled),o.style.backgroundColor=o.style.borderLeftColor=o.style.borderRightColor="",i.style.backgroundColor=i.style.borderLeftColor=i.style.borderRightColor=""),e.reverseButtons&&o.parentNode.insertBefore(i,o)}var st,ut=function(t,e){J(t,e,!0)},lt=function(t,e){J(t,e,!1)},dt=function(t,e){for(var n=0;n<t.childNodes.length;n++)if(V(t.childNodes[n],e))return t.childNodes[n]},pt=function(t){return!(!t||!(t.offsetWidth||t.offsetHeight||t.getClientRects().length))},ft='\n <div aria-labelledby="'.concat(F.title,'" aria-describedby="').concat(F.content,'" class="').concat(F.popup,'" tabindex="-1">\n   <div class="').concat(F.header,'">\n     <ul class="').concat(F["progress-steps"],'"></ul>\n     <div class="').concat(F.icon," ").concat(z.error,'"></div>\n     <div class="').concat(F.icon," ").concat(z.question,'"></div>\n     <div class="').concat(F.icon," ").concat(z.warning,'"></div>\n     <div class="').concat(F.icon," ").concat(z.info,'"></div>\n     <div class="').concat(F.icon," ").concat(z.success,'"></div>\n     <img class="').concat(F.image,'" />\n     <h2 class="').concat(F.title,'" id="').concat(F.title,'"></h2>\n     <button type="button" class="').concat(F.close,'"></button>\n   </div>\n   <div class="').concat(F.content,'">\n     <div id="').concat(F.content,'" class="').concat(F["html-container"],'"></div>\n     <input class="').concat(F.input,'" />\n     <input type="file" class="').concat(F.file,'" />\n     <div class="').concat(F.range,'">\n       <input type="range" />\n       <output></output>\n     </div>\n     <select class="').concat(F.select,'"></select>\n     <div class="').concat(F.radio,'"></div>\n     <label for="').concat(F.checkbox,'" class="').concat(F.checkbox,'">\n       <input type="checkbox" />\n       <span class="').concat(F.label,'"></span>\n     </label>\n     <textarea class="').concat(F.textarea,'"></textarea>\n     <div class="').concat(F["validation-message"],'" id="').concat(F["validation-message"],'"></div>\n   </div>\n   <div class="').concat(F.actions,'">\n     <button type="button" class="').concat(F.confirm,'">OK</button>\n     <button type="button" class="').concat(F.cancel,'">Cancel</button>\n   </div>\n   <div class="').concat(F.footer,'"></div>\n   <div class="').concat(F["timer-progress-bar-container"],'">\n     <div class="').concat(F["timer-progress-bar"],'"></div>\n   </div>\n </div>\n').replace(/(^|\n)\s*/g,""),mt=function(t){var e,n=!!(e=W())&&(e.parentNode.removeChild(e),lt([document.documentElement,document.body],[F["no-backdrop"],F["toast-shown"],F["has-column"]]),!0);if(it())v("SweetAlert2 requires document to initialize");else{var o=document.createElement("div");o.className=F.container,n&&ut(o,F["no-transition"]),M(o,ft);var i,r,a,c,s,u,l,d,p,f,m,h,g="string"==typeof(i=t.target)?document.querySelector(i):i;g.appendChild(o),r=t,(a=K()).setAttribute("role",r.toast?"alert":"dialog"),a.setAttribute("aria-live",r.toast?"polite":"assertive"),r.toast||a.setAttribute("aria-modal","true"),c=g,"rtl"===window.getComputedStyle(c).direction&&ut(W(),F.rtl),s=k(),u=dt(s,F.input),l=dt(s,F.file),d=s.querySelector(".".concat(F.range," input")),p=s.querySelector(".".concat(F.range," output")),f=dt(s,F.select),m=s.querySelector(".".concat(F.checkbox," input")),h=dt(s,F.textarea),u.oninput=rt,l.onchange=rt,f.onchange=rt,m.onchange=rt,h.oninput=rt,d.oninput=function(t){rt(t),p.value=d.value},d.onchange=function(t){rt(t),d.nextSibling.value=d.value}}},ht=function(t,e){t.jquery?gt(e,t):M(e,t.toString())},gt=function(t,e){if(t.textContent="",0 in e)for(var n=0;n in e;n++)t.appendChild(e[n].cloneNode(!0));else t.appendChild(e.cloneNode(!0))},vt=function(){if(it())return!1;var t=document.createElement("div"),e={WebkitAnimation:"webkitAnimationEnd",OAnimation:"oAnimationEnd oanimationend",animation:"animationend"};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&void 0!==t.style[n])return e[n];return!1}();function bt(t,e,n){var o;et(t,n["show".concat((o=e).charAt(0).toUpperCase()+o.slice(1),"Button")],"inline-block"),M(t,n["".concat(e,"ButtonText")]),t.setAttribute("aria-label",n["".concat(e,"ButtonAriaLabel")]),t.className=F[e],R(t,n,"".concat(e,"Button")),ut(t,n["".concat(e,"ButtonClass")])}function yt(t,e){var n=W();if(n){var o,i,r,a;o=n,"string"==typeof(i=e.backdrop)?o.style.background=i:i||ut([document.documentElement,document.body],F["no-backdrop"]),!e.backdrop&&e.allowOutsideClick&&D('"allowOutsideClick" parameter requires `backdrop` parameter to be set to `true`'),r=n,(a=e.position)in F?ut(r,F[a]):(D('The "position" parameter is not valid, defaulting to "center"'),ut(r,F.center)),function(t,e){if(e&&"string"==typeof e){var n="grow-".concat(e);n in F&&ut(t,F[n])}}(n,e.grow),R(n,e,"container");var c=document.body.getAttribute("data-swal2-queue-step");c&&(n.setAttribute("data-queue-step",c),document.body.removeAttribute("data-swal2-queue-step"))}}function wt(t,e){t.placeholder&&!e.inputPlaceholder||(t.placeholder=e.inputPlaceholder)}var Ct={promise:new WeakMap,innerParams:new WeakMap,domCache:new WeakMap},kt=["input","file","range","select","radio","checkbox","textarea"],xt=function(t){if(!St[t.input])return v('Unexpected type of input! Expected "text", "email", "password", "number", "tel", "select", "radio", "checkbox", "textarea", "file" or "url", got "'.concat(t.input,'"'));var e=Bt(t.input),n=St[t.input](e,t);G(n),setTimeout(function(){$(n)})},Pt=function(t,e){var n=Q(k(),t);if(n)for(var o in!function(t){for(var e=0;e<t.attributes.length;e++){var n=t.attributes[e].name;-1===["type","value","style"].indexOf(n)&&t.removeAttribute(n)}}(n),e)"range"===t&&"placeholder"===o||n.setAttribute(o,e[o])},At=function(t){var e=Bt(t.input);t.customClass&&ut(e,t.customClass.input)},Bt=function(t){var e=F[t]?F[t]:F.input;return dt(k(),e)},St={};St.text=St.email=St.password=St.number=St.tel=St.url=function(t,e){return"string"==typeof e.inputValue||"number"==typeof e.inputValue?t.value=e.inputValue:g(e.inputValue)||D('Unexpected type of inputValue! Expected "string", "number" or "Promise", got "'.concat(r(e.inputValue),'"')),wt(t,e),t.type=e.input,t},St.file=function(t,e){return wt(t,e),t},St.range=function(t,e){var n=t.querySelector("input"),o=t.querySelector("output");return n.value=e.inputValue,n.type=e.input,o.value=e.inputValue,t},St.select=function(t,e){if(t.textContent="",e.inputPlaceholder){var n=document.createElement("option");M(n,e.inputPlaceholder),n.value="",n.disabled=!0,n.selected=!0,t.appendChild(n)}return t},St.radio=function(t){return t.textContent="",t},St.checkbox=function(t,e){var n=Q(k(),"checkbox");n.value=1,n.id=F.checkbox,n.checked=Boolean(e.inputValue);var o=t.querySelector("span");return M(o,e.inputPlaceholder),t},St.textarea=function(e,t){if(e.value=t.inputValue,wt(e,t),"MutationObserver"in window){var n=parseInt(window.getComputedStyle(K()).width),o=parseInt(window.getComputedStyle(K()).paddingLeft)+parseInt(window.getComputedStyle(K()).paddingRight);new MutationObserver(function(){var t=e.offsetWidth+o;K().style.width=n<t?"".concat(t,"px"):null}).observe(e,{attributes:!0,attributeFilter:["style"]})}return e};function Et(t,e){var n,o,i,r,a,c=k().querySelector("#".concat(F.content));e.html?(at(e.html,c),G(c,"block")):e.text?(c.textContent=e.text,G(c,"block")):tt(c),n=t,o=e,i=k(),r=Ct.innerParams.get(n),a=!r||o.input!==r.input,kt.forEach(function(t){var e=F[t],n=dt(i,e);Pt(t,o.inputAttributes),n.className=e,a&&tt(n)}),o.input&&(a&&xt(o),At(o)),R(k(),e,"content")}function Ot(){return W().getAttribute("data-queue-step")}function Tt(t,s){var u=P();if(!s.progressSteps||0===s.progressSteps.length)return tt(u);G(u),u.textContent="";var l=parseInt(void 0===s.currentProgressStep?Ot():s.currentProgressStep);l>=s.progressSteps.length&&D("Invalid currentProgressStep parameter, it should be less than progressSteps.length (currentProgressStep like JS arrays starts from 0)"),s.progressSteps.forEach(function(t,e){var n,o,i,r,a=(n=t,o=document.createElement("li"),ut(o,F["progress-step"]),M(o,n),o);if(u.appendChild(a),e===l&&ut(a,F["active-progress-step"]),e!==s.progressSteps.length-1){var c=(i=t,r=document.createElement("li"),ut(r,F["progress-step-line"]),i.progressStepsDistance&&(r.style.width=i.progressStepsDistance),r);u.appendChild(c)}})}function Lt(t,e){var n,o,i,r,a=O();R(a,e,"header"),Tt(0,e),function(t,e){var n=Ct.innerParams.get(t);if(n&&e.icon===n.icon&&w())R(w(),e,"icon");else if(jt(),e.icon)if(-1!==Object.keys(z).indexOf(e.icon)){var o=y(".".concat(F.icon,".").concat(z[e.icon]));G(o),Vt(o,e),Mt(),R(o,e,"icon"),ut(o,e.showClass.icon)}else v('Unknown icon! Expected "success", "error", "warning", "info" or "question", got "'.concat(e.icon,'"'))}(t,e),function(t){var e=x();if(!t.imageUrl)return tt(e);G(e),e.setAttribute("src",t.imageUrl),e.setAttribute("alt",t.imageAlt),X(e,"width",t.imageWidth),X(e,"height",t.imageHeight),e.className=F.image,R(e,t,"image")}(e),n=e,o=C(),et(o,n.title||n.titleText),n.title&&at(n.title,o),n.titleText&&(o.innerText=n.titleText),R(o,n,"title"),i=e,r=q(),M(r,i.closeButtonHtml),R(r,i,"closeButton"),et(r,i.showCloseButton),r.setAttribute("aria-label",i.closeButtonAriaLabel)}function qt(t,e){var n,o,i,r;n=e,o=K(),X(o,"width",n.width),X(o,"padding",n.padding),n.background&&(o.style.background=n.background),Dt(o,n),yt(0,e),Lt(t,e),Et(t,e),ct(0,e),i=e,r=T(),et(r,i.footer),i.footer&&at(i.footer,r),R(r,i,"footer"),"function"==typeof e.onRender&&e.onRender(K())}function It(){return B()&&B().click()}var jt=function(){for(var t=n(),e=0;e<t.length;e++)tt(t[e])},Mt=function(){for(var t=K(),e=window.getComputedStyle(t).getPropertyValue("background-color"),n=t.querySelectorAll("[class^=swal2-success-circular-line], .swal2-success-fix"),o=0;o<n.length;o++)n[o].style.backgroundColor=e},Vt=function(t,e){if(t.textContent="",e.iconHtml)M(t,Rt(e.iconHtml));else if("success"===e.icon)M(t,'\n      <div class="swal2-success-circular-line-left"></div>\n      <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>\n      <div class="swal2-success-ring"></div> <div class="swal2-success-fix"></div>\n      <div class="swal2-success-circular-line-right"></div>\n    ');else if("error"===e.icon)M(t,'\n      <span class="swal2-x-mark">\n        <span class="swal2-x-mark-line-left"></span>\n        <span class="swal2-x-mark-line-right"></span>\n      </span>\n    ');else{M(t,Rt({question:"?",warning:"!",info:"i"}[e.icon]))}},Rt=function(t){return'<div class="'.concat(F["icon-content"],'">').concat(t,"</div>")},Ht=[],Dt=function(t,e){t.className="".concat(F.popup," ").concat(pt(t)?e.showClass.popup:""),e.toast?(ut([document.documentElement,document.body],F["toast-shown"]),ut(t,F.toast)):ut(t,F.modal),R(t,e,"popup"),"string"==typeof e.customClass&&ut(t,e.customClass),e.icon&&ut(t,F["icon-".concat(e.icon)])};function Nt(){var t=K();t||Ge.fire(),t=K();var e=E(),n=B();G(e),G(n,"inline-block"),ut([t,e],F.loading),n.disabled=!0,t.setAttribute("data-loading",!0),t.setAttribute("aria-busy",!0),t.focus()}function Ut(){return new Promise(function(t){var e=window.scrollX,n=window.scrollY;Kt.restoreFocusTimeout=setTimeout(function(){Kt.previousActiveElement&&Kt.previousActiveElement.focus?(Kt.previousActiveElement.focus(),Kt.previousActiveElement=null):document.body&&document.body.focus(),t()},100),void 0!==e&&void 0!==n&&window.scrollTo(e,n)})}function _t(){if(Kt.timeout)return function(){var t=L(),e=parseInt(window.getComputedStyle(t).width);t.style.removeProperty("transition"),t.style.width="100%";var n=parseInt(window.getComputedStyle(t).width),o=parseInt(e/n*100);t.style.removeProperty("transition"),t.style.width="".concat(o,"%")}(),Kt.timeout.stop()}function Ft(){if(Kt.timeout){var t=Kt.timeout.start();return ot(t),t}}function zt(t){return Object.prototype.hasOwnProperty.call(Yt,t)}function Wt(t){return Qt[t]}var Kt={},Yt={title:"",titleText:"",text:"",html:"",footer:"",icon:void 0,iconHtml:void 0,toast:!1,animation:!0,showClass:{popup:"swal2-show",backdrop:"swal2-backdrop-show",icon:"swal2-icon-show"},hideClass:{popup:"swal2-hide",backdrop:"swal2-backdrop-hide",icon:"swal2-icon-hide"},customClass:void 0,target:"body",backdrop:!0,heightAuto:!0,allowOutsideClick:!0,allowEscapeKey:!0,allowEnterKey:!0,stopKeydownPropagation:!0,keydownListenerCapture:!1,showConfirmButton:!0,showCancelButton:!1,preConfirm:void 0,confirmButtonText:"OK",confirmButtonAriaLabel:"",confirmButtonColor:void 0,cancelButtonText:"Cancel",cancelButtonAriaLabel:"",cancelButtonColor:void 0,buttonsStyling:!0,reverseButtons:!1,focusConfirm:!0,focusCancel:!1,showCloseButton:!1,closeButtonHtml:"&times;",closeButtonAriaLabel:"Close this dialog",showLoaderOnConfirm:!1,imageUrl:void 0,imageWidth:void 0,imageHeight:void 0,imageAlt:"",timer:void 0,timerProgressBar:!1,width:void 0,padding:void 0,background:void 0,input:void 0,inputPlaceholder:"",inputValue:"",inputOptions:{},inputAutoTrim:!0,inputAttributes:{},inputValidator:void 0,validationMessage:void 0,grow:!1,position:"center",progressSteps:[],currentProgressStep:void 0,progressStepsDistance:void 0,onBeforeOpen:void 0,onOpen:void 0,onRender:void 0,onClose:void 0,onAfterClose:void 0,onDestroy:void 0,scrollbarPadding:!0},Zt=["title","titleText","text","html","icon","hideClass","customClass","allowOutsideClick","allowEscapeKey","showConfirmButton","showCancelButton","confirmButtonText","confirmButtonAriaLabel","confirmButtonColor","cancelButtonText","cancelButtonAriaLabel","cancelButtonColor","buttonsStyling","reverseButtons","imageUrl","imageWidth","imageHeight","imageAlt","progressSteps","currentProgressStep"],Qt={animation:'showClass" and "hideClass'},$t=["allowOutsideClick","allowEnterKey","backdrop","focusConfirm","focusCancel","heightAuto","keydownListenerCapture"],Jt=Object.freeze({isValidParameter:zt,isUpdatableParameter:function(t){return-1!==Zt.indexOf(t)},isDeprecatedParameter:Wt,argsToParams:function(o){var i={};return"object"!==r(o[0])||b(o[0])?["title","html","icon"].forEach(function(t,e){var n=o[e];"string"==typeof n||b(n)?i[t]=n:void 0!==n&&v("Unexpected type of ".concat(t,'! Expected "string" or "Element", got ').concat(r(n)))}):c(i,o[0]),i},isVisible:function(){return pt(K())},clickConfirm:It,clickCancel:function(){return S()&&S().click()},getContainer:W,getPopup:K,getTitle:C,getContent:k,getHtmlContainer:function(){return e(F["html-container"])},getImage:x,getIcon:w,getIcons:n,getCloseButton:q,getActions:E,getConfirmButton:B,getCancelButton:S,getHeader:O,getFooter:T,getTimerProgressBar:L,getFocusableElements:I,getValidationMessage:A,isLoading:function(){return K().hasAttribute("data-loading")},fire:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return l(this,e)},mixin:function(n){return function(t){function e(){return o(this,e),d(this,s(e).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&u(t,e)}(e,t),a(e,[{key:"_main",value:function(t){return p(s(e.prototype),"_main",this).call(this,c({},n,t))}}]),e}(this)},queue:function(t){var r=this;Ht=t;function a(t,e){Ht=[],t(e)}var c=[];return new Promise(function(i){!function e(n,o){n<Ht.length?(document.body.setAttribute("data-swal2-queue-step",n),r.fire(Ht[n]).then(function(t){void 0!==t.value?(c.push(t.value),e(n+1,o)):a(i,{dismiss:t.dismiss})})):a(i,{value:c})}(0)})},getQueueStep:Ot,insertQueueStep:function(t,e){return e&&e<Ht.length?Ht.splice(e,0,t):Ht.push(t)},deleteQueueStep:function(t){void 0!==Ht[t]&&Ht.splice(t,1)},showLoading:Nt,enableLoading:Nt,getTimerLeft:function(){return Kt.timeout&&Kt.timeout.getTimerLeft()},stopTimer:_t,resumeTimer:Ft,toggleTimer:function(){var t=Kt.timeout;return t&&(t.running?_t:Ft)()},increaseTimer:function(t){if(Kt.timeout){var e=Kt.timeout.increase(t);return ot(e,!0),e}},isTimerRunning:function(){return Kt.timeout&&Kt.timeout.isRunning()}});function Xt(){var t=Ct.innerParams.get(this);if(t){var e=Ct.domCache.get(this);t.showConfirmButton||(tt(e.confirmButton),t.showCancelButton||tt(e.actions)),lt([e.popup,e.actions],F.loading),e.popup.removeAttribute("aria-busy"),e.popup.removeAttribute("data-loading"),e.confirmButton.disabled=!1,e.cancelButton.disabled=!1}}function Gt(){null===Z.previousBodyPadding&&document.body.scrollHeight>window.innerHeight&&(Z.previousBodyPadding=parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right")),document.body.style.paddingRight="".concat(Z.previousBodyPadding+function(){var t=document.createElement("div");t.className=F["scrollbar-measure"],document.body.appendChild(t);var e=t.getBoundingClientRect().width-t.clientWidth;return document.body.removeChild(t),e}(),"px"))}function te(){return!!window.MSInputMethodContext&&!!document.documentMode}function ee(){var t=W(),e=K();t.style.removeProperty("align-items"),e.offsetTop<0&&(t.style.alignItems="flex-start")}var ne=function(){var n,o=W();o.ontouchstart=function(t){var e;n=t.target===o||!((e=o).scrollHeight>e.clientHeight)&&"INPUT"!==t.target.tagName},o.ontouchmove=function(t){n&&(t.preventDefault(),t.stopPropagation())}},oe={swalPromiseResolve:new WeakMap};function ie(t,e,n,o){n?ce(t,o):(Ut().then(function(){return ce(t,o)}),Kt.keydownTarget.removeEventListener("keydown",Kt.keydownHandler,{capture:Kt.keydownListenerCapture}),Kt.keydownHandlerAdded=!1),e.parentNode&&!document.body.getAttribute("data-swal2-queue-step")&&e.parentNode.removeChild(e),j()&&(null!==Z.previousBodyPadding&&(document.body.style.paddingRight="".concat(Z.previousBodyPadding,"px"),Z.previousBodyPadding=null),function(){if(V(document.body,F.iosfix)){var t=parseInt(document.body.style.top,10);lt(document.body,F.iosfix),document.body.style.top="",document.body.scrollTop=-1*t}}(),"undefined"!=typeof window&&te()&&window.removeEventListener("resize",ee),m(document.body.children).forEach(function(t){t.hasAttribute("data-previous-aria-hidden")?(t.setAttribute("aria-hidden",t.getAttribute("data-previous-aria-hidden")),t.removeAttribute("data-previous-aria-hidden")):t.removeAttribute("aria-hidden")})),lt([document.documentElement,document.body],[F.shown,F["height-auto"],F["no-backdrop"],F["toast-shown"],F["toast-column"]])}function re(t){var e=K();if(e){var n=Ct.innerParams.get(this);if(n&&!V(e,n.hideClass.popup)){var o=oe.swalPromiseResolve.get(this);lt(e,n.showClass.popup),ut(e,n.hideClass.popup);var i=W();lt(i,n.showClass.backdrop),ut(i,n.hideClass.backdrop),function(t,e,n){var o=W(),i=vt&&nt(e),r=n.onClose,a=n.onAfterClose;if(r!==null&&typeof r==="function"){r(e)}if(i){ae(t,e,o,a)}else{ie(t,o,Y(),a)}}(this,e,n),o(t||{})}}}var ae=function(t,e,n,o){Kt.swalCloseEventFinishedCallback=ie.bind(null,t,n,Y(),o),e.addEventListener(vt,function(t){t.target===e&&(Kt.swalCloseEventFinishedCallback(),delete Kt.swalCloseEventFinishedCallback)})},ce=function(t,e){setTimeout(function(){"function"==typeof e&&e(),t._destroy()})};function se(t,e,n){var o=Ct.domCache.get(t);e.forEach(function(t){o[t].disabled=n})}function ue(t,e){if(!t)return!1;if("radio"===t.type)for(var n=t.parentNode.parentNode.querySelectorAll("input"),o=0;o<n.length;o++)n[o].disabled=e;else t.disabled=e}var le=function(){function n(t,e){o(this,n),this.callback=t,this.remaining=e,this.running=!1,this.start()}return a(n,[{key:"start",value:function(){return this.running||(this.running=!0,this.started=new Date,this.id=setTimeout(this.callback,this.remaining)),this.remaining}},{key:"stop",value:function(){return this.running&&(this.running=!1,clearTimeout(this.id),this.remaining-=new Date-this.started),this.remaining}},{key:"increase",value:function(t){var e=this.running;return e&&this.stop(),this.remaining+=t,e&&this.start(),this.remaining}},{key:"getTimerLeft",value:function(){return this.running&&(this.stop(),this.start()),this.remaining}},{key:"isRunning",value:function(){return this.running}}]),n}(),de={email:function(t,e){return/^[a-zA-Z0-9.+_-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-]{2,24}$/.test(t)?Promise.resolve():Promise.resolve(e||"Invalid email address")},url:function(t,e){return/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{2,256}\.[a-z]{2,63}\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(t)?Promise.resolve():Promise.resolve(e||"Invalid URL")}};function pe(t){var e,n;(e=t).inputValidator||Object.keys(de).forEach(function(t){e.input===t&&(e.inputValidator=de[t])}),t.showLoaderOnConfirm&&!t.preConfirm&&D("showLoaderOnConfirm is set to true, but preConfirm is not defined.\nshowLoaderOnConfirm should be used together with preConfirm, see usage example:\nhttps://sweetalert2.github.io/#ajax-request"),t.animation=U(t.animation),(n=t).target&&("string"!=typeof n.target||document.querySelector(n.target))&&("string"==typeof n.target||n.target.appendChild)||(D('Target parameter is not valid, defaulting to "body"'),n.target="body"),"string"==typeof t.title&&(t.title=t.title.split("\n").join("<br />")),mt(t)}function fe(t){var e=W(),n=K();"function"==typeof t.onBeforeOpen&&t.onBeforeOpen(n),Pe(e,n,t),ke(e,n),j()&&xe(e,t.scrollbarPadding),Y()||Kt.previousActiveElement||(Kt.previousActiveElement=document.activeElement),"function"==typeof t.onOpen&&setTimeout(function(){return t.onOpen(n)}),lt(e,F["no-transition"])}function me(t){var e=K();if(t.target===e){var n=W();e.removeEventListener(vt,me),n.style.overflowY="auto"}}function he(t,e){"select"===e.input||"radio"===e.input?Ee(t,e):-1!==["text","email","number","tel","textarea"].indexOf(e.input)&&g(e.inputValue)&&Oe(t,e)}function ge(t,e){t.disableButtons(),e.input?qe(t,e):Ie(t,e,!0)}function ve(t,e){t.disableButtons(),e(_.cancel)}function be(t,e){t.closePopup({value:e})}function ye(e,t,n,o){t.keydownTarget&&t.keydownHandlerAdded&&(t.keydownTarget.removeEventListener("keydown",t.keydownHandler,{capture:t.keydownListenerCapture}),t.keydownHandlerAdded=!1),n.toast||(t.keydownHandler=function(t){return Ve(e,t,o)},t.keydownTarget=n.keydownListenerCapture?window:K(),t.keydownListenerCapture=n.keydownListenerCapture,t.keydownTarget.addEventListener("keydown",t.keydownHandler,{capture:t.keydownListenerCapture}),t.keydownHandlerAdded=!0)}function we(t,e,n){var o=I(),i=0;if(i<o.length)return(e+=n)===o.length?e=0:-1===e&&(e=o.length-1),o[e].focus();K().focus()}function Ce(t,e,n){Ct.innerParams.get(t).toast?Ue(t,e,n):(Fe(e),ze(e),We(t,e,n))}var ke=function(t,e){vt&&nt(e)?(t.style.overflowY="hidden",e.addEventListener(vt,me)):t.style.overflowY="auto"},xe=function(t,e){!function(){if((/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream||"MacIntel"===navigator.platform&&1<navigator.maxTouchPoints)&&!V(document.body,F.iosfix)){var t=document.body.scrollTop;document.body.style.top="".concat(-1*t,"px"),ut(document.body,F.iosfix),ne()}}(),"undefined"!=typeof window&&te()&&(ee(),window.addEventListener("resize",ee)),m(document.body.children).forEach(function(t){t===W()||function(t,e){if("function"==typeof t.contains)return t.contains(e)}(t,W())||(t.hasAttribute("aria-hidden")&&t.setAttribute("data-previous-aria-hidden",t.getAttribute("aria-hidden")),t.setAttribute("aria-hidden","true"))}),e&&Gt(),setTimeout(function(){t.scrollTop=0})},Pe=function(t,e,n){ut(t,n.showClass.backdrop),G(e),ut(e,n.showClass.popup),ut([document.documentElement,document.body],F.shown),n.heightAuto&&n.backdrop&&!n.toast&&ut([document.documentElement,document.body],F["height-auto"])},Ae=function(t){return t.checked?1:0},Be=function(t){return t.checked?t.value:null},Se=function(t){return t.files.length?null!==t.getAttribute("multiple")?t.files:t.files[0]:null},Ee=function(e,n){function o(t){return Te[n.input](i,Le(t),n)}var i=k();g(n.inputOptions)?(Nt(),n.inputOptions.then(function(t){e.hideLoading(),o(t)})):"object"===r(n.inputOptions)?o(n.inputOptions):v("Unexpected type of inputOptions! Expected object, Map or Promise, got ".concat(r(n.inputOptions)))},Oe=function(e,n){var o=e.getInput();tt(o),n.inputValue.then(function(t){o.value="number"===n.input?parseFloat(t)||0:"".concat(t),G(o),o.focus(),e.hideLoading()}).catch(function(t){v("Error in inputValue promise: ".concat(t)),o.value="",G(o),o.focus(),e.hideLoading()})},Te={select:function(t,e,i){var r=dt(t,F.select);e.forEach(function(t){var e=t[0],n=t[1],o=document.createElement("option");o.value=e,M(o,n),i.inputValue.toString()===e.toString()&&(o.selected=!0),r.appendChild(o)}),r.focus()},radio:function(t,e,a){var c=dt(t,F.radio);e.forEach(function(t){var e=t[0],n=t[1],o=document.createElement("input"),i=document.createElement("label");o.type="radio",o.name=F.radio,o.value=e,a.inputValue.toString()===e.toString()&&(o.checked=!0);var r=document.createElement("span");M(r,n),r.className=F.label,i.appendChild(o),i.appendChild(r),c.appendChild(i)});var n=c.querySelectorAll("input");n.length&&n[0].focus()}},Le=function(e){var n=[];return"undefined"!=typeof Map&&e instanceof Map?e.forEach(function(t,e){n.push([e,t])}):Object.keys(e).forEach(function(t){n.push([t,e[t]])}),n},qe=function(e,n){var o=function(t,e){var n=t.getInput();if(!n)return null;switch(e.input){case"checkbox":return Ae(n);case"radio":return Be(n);case"file":return Se(n);default:return e.inputAutoTrim?n.value.trim():n.value}}(e,n);n.inputValidator?(e.disableInput(),Promise.resolve().then(function(){return n.inputValidator(o,n.validationMessage)}).then(function(t){e.enableButtons(),e.enableInput(),t?e.showValidationMessage(t):Ie(e,n,o)})):e.getInput().checkValidity()?Ie(e,n,o):(e.enableButtons(),e.showValidationMessage(n.validationMessage))},Ie=function(e,t,n){(t.showLoaderOnConfirm&&Nt(),t.preConfirm)?(e.resetValidationMessage(),Promise.resolve().then(function(){return t.preConfirm(n,t.validationMessage)}).then(function(t){pt(A())||!1===t?e.hideLoading():be(e,void 0===t?n:t)})):be(e,n)},je=["ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Left","Right","Up","Down"],Me=["Escape","Esc"],Ve=function(t,e,n){var o=Ct.innerParams.get(t);o.stopKeydownPropagation&&e.stopPropagation(),"Enter"===e.key?Re(t,e,o):"Tab"===e.key?He(e,o):-1!==je.indexOf(e.key)?De():-1!==Me.indexOf(e.key)&&Ne(e,o,n)},Re=function(t,e,n){if(!e.isComposing&&e.target&&t.getInput()&&e.target.outerHTML===t.getInput().outerHTML){if(-1!==["textarea","file"].indexOf(n.input))return;It(),e.preventDefault()}},He=function(t){for(var e=t.target,n=I(),o=-1,i=0;i<n.length;i++)if(e===n[i]){o=i;break}t.shiftKey?we(0,o,-1):we(0,o,1),t.stopPropagation(),t.preventDefault()},De=function(){var t=B(),e=S();document.activeElement===t&&pt(e)?e.focus():document.activeElement===e&&pt(t)&&t.focus()},Ne=function(t,e,n){U(e.allowEscapeKey)&&(t.preventDefault(),n(_.esc))},Ue=function(e,t,n){t.popup.onclick=function(){var t=Ct.innerParams.get(e);t.showConfirmButton||t.showCancelButton||t.showCloseButton||t.input||n(_.close)}},_e=!1,Fe=function(e){e.popup.onmousedown=function(){e.container.onmouseup=function(t){e.container.onmouseup=void 0,t.target===e.container&&(_e=!0)}}},ze=function(e){e.container.onmousedown=function(){e.popup.onmouseup=function(t){e.popup.onmouseup=void 0,t.target!==e.popup&&!e.popup.contains(t.target)||(_e=!0)}}},We=function(n,o,i){o.container.onclick=function(t){var e=Ct.innerParams.get(n);_e?_e=!1:t.target===o.container&&U(e.allowOutsideClick)&&i(_.backdrop)}};var Ke=function(t,e,n){var o=L();tt(o),e.timer&&(t.timeout=new le(function(){n("timer"),delete t.timeout},e.timer),e.timerProgressBar&&(G(o),setTimeout(function(){t.timeout.running&&ot(e.timer)})))},Ye=function(t,e){if(!e.toast)return U(e.allowEnterKey)?e.focusCancel&&pt(t.cancelButton)?t.cancelButton.focus():e.focusConfirm&&pt(t.confirmButton)?t.confirmButton.focus():void we(0,-1,1):Ze()},Ze=function(){document.activeElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur()};var Qe,$e=function(t){for(var e in t)t[e]=new WeakMap},Je=Object.freeze({hideLoading:Xt,disableLoading:Xt,getInput:function(t){var e=Ct.innerParams.get(t||this),n=Ct.domCache.get(t||this);return n?Q(n.content,e.input):null},close:re,closePopup:re,closeModal:re,closeToast:re,enableButtons:function(){se(this,["confirmButton","cancelButton"],!1)},disableButtons:function(){se(this,["confirmButton","cancelButton"],!0)},enableInput:function(){return ue(this.getInput(),!1)},disableInput:function(){return ue(this.getInput(),!0)},showValidationMessage:function(t){var e=Ct.domCache.get(this);M(e.validationMessage,t);var n=window.getComputedStyle(e.popup);e.validationMessage.style.marginLeft="-".concat(n.getPropertyValue("padding-left")),e.validationMessage.style.marginRight="-".concat(n.getPropertyValue("padding-right")),G(e.validationMessage);var o=this.getInput();o&&(o.setAttribute("aria-invalid",!0),o.setAttribute("aria-describedBy",F["validation-message"]),$(o),ut(o,F.inputerror))},resetValidationMessage:function(){var t=Ct.domCache.get(this);t.validationMessage&&tt(t.validationMessage);var e=this.getInput();e&&(e.removeAttribute("aria-invalid"),e.removeAttribute("aria-describedBy"),lt(e,F.inputerror))},getProgressSteps:function(){return Ct.domCache.get(this).progressSteps},_main:function(t){!function(t){for(var e in t)zt(i=e)||D('Unknown parameter "'.concat(i,'"')),t.toast&&(o=e,-1!==$t.indexOf(o)&&D('The parameter "'.concat(o,'" is incompatible with toasts'))),Wt(n=e)&&h(n,Wt(n));var n,o,i}(t),Kt.currentInstance&&Kt.currentInstance._destroy(),Kt.currentInstance=this;var e=function(t){var e=c({},Yt.showClass,t.showClass),n=c({},Yt.hideClass,t.hideClass),o=c({},Yt,t);if(o.showClass=e,o.hideClass=n,t.animation===false){o.showClass={popup:"swal2-noanimation",backdrop:"swal2-noanimation"};o.hideClass={}}return o}(t);pe(e),Object.freeze(e),Kt.timeout&&(Kt.timeout.stop(),delete Kt.timeout),clearTimeout(Kt.restoreFocusTimeout);var n=function(t){var e={popup:K(),container:W(),content:k(),actions:E(),confirmButton:B(),cancelButton:S(),closeButton:q(),validationMessage:A(),progressSteps:P()};return Ct.domCache.set(t,e),e}(this);return qt(this,e),Ct.innerParams.set(this,e),function(n,o,i){return new Promise(function(t){var e=function t(e){n.closePopup({dismiss:e})};oe.swalPromiseResolve.set(n,t);o.confirmButton.onclick=function(){return ge(n,i)};o.cancelButton.onclick=function(){return ve(n,e)};o.closeButton.onclick=function(){return e(_.close)};Ce(n,o,e);ye(n,Kt,i,e);if(i.toast&&(i.input||i.footer||i.showCloseButton)){ut(document.body,F["toast-column"])}else{lt(document.body,F["toast-column"])}he(n,i);fe(i);Ke(Kt,i,e);Ye(o,i);o.container.scrollTop=0})}(this,n,e)},update:function(e){var t=K(),n=Ct.innerParams.get(this);if(!t||V(t,n.hideClass.popup))return D("You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.");var o={};Object.keys(e).forEach(function(t){Ge.isUpdatableParameter(t)?o[t]=e[t]:D('Invalid parameter to update: "'.concat(t,'". Updatable params are listed here: https://github.com/sweetalert2/sweetalert2/blob/master/src/utils/params.js'))});var i=c({},n,o);qt(this,i),Ct.innerParams.set(this,i),Object.defineProperties(this,{params:{value:c({},this.params,e),writable:!1,enumerable:!0}})},_destroy:function(){var t=Ct.domCache.get(this),e=Ct.innerParams.get(this);e&&(t.popup&&Kt.swalCloseEventFinishedCallback&&(Kt.swalCloseEventFinishedCallback(),delete Kt.swalCloseEventFinishedCallback),Kt.deferDisposalTimer&&(clearTimeout(Kt.deferDisposalTimer),delete Kt.deferDisposalTimer),"function"==typeof e.onDestroy&&e.onDestroy(),delete this.params,delete Kt.keydownHandler,delete Kt.keydownTarget,$e(Ct),$e(oe))}});function Xe(){if("undefined"!=typeof window){"undefined"==typeof Promise&&v("This package requires a Promise library, please include a shim to enable it in this browser (See: https://github.com/sweetalert2/sweetalert2/wiki/Migration-from-SweetAlert-to-SweetAlert2#1-ie-support)"),Qe=this;for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var o=Object.freeze(this.constructor.argsToParams(e));Object.defineProperties(this,{params:{value:o,writable:!1,enumerable:!0,configurable:!0}});var i=this._main(this.params);Ct.promise.set(this,i)}}Xe.prototype.then=function(t){return Ct.promise.get(this).then(t)},Xe.prototype.finally=function(t){return Ct.promise.get(this).finally(t)},c(Xe.prototype,Je),c(Xe,Jt),Object.keys(Je).forEach(function(t){Xe[t]=function(){if(Qe)return Qe[t].apply(Qe,arguments)}}),Xe.DismissReason=_,Xe.version="9.10.8";var Ge=Xe;return Ge.default=Ge}),void 0!==this&&this.Sweetalert2&&(this.swal=this.sweetAlert=this.Swal=this.SweetAlert=this.Sweetalert2);