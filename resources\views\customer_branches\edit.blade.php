@extends('layouts.index')

@section('content')
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card card-primary">
                <div class="card-header">
                    <h3 class="card-title">Şube Düzenle - {{ $customer->company_name }}</h3>
                </div>
                <div class="card-body">
                    <form action="{{ route('customers.customer-branches.update', [$customer->id, $branch->id]) }}" method="POST">
                        @csrf
                        @method('PUT')
                        <div class="form-group mb-3">
                            <label for="branch_name" class="form-label">Şube Adı <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="branch_name" name="branch_name" value="{{ old('branch_name', $branch->branch_name) }}" required>
                            @error('branch_name')<div class="text-danger">{{ $message }}</div>@enderror
                        </div>
                        <div class="form-group mb-3">
                            <label for="address" class="form-label">Adres</label>
                            <input type="text" class="form-control" id="address" name="address" value="{{ old('address', $branch->address) }}">
                            @error('address')<div class="text-danger">{{ $message }}</div>@enderror
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="city" class="form-label">Şehir</label>
                                    <input type="text" class="form-control" id="city" name="city" value="{{ old('city', $branch->city) }}">
                                    @error('city')<div class="text-danger">{{ $message }}</div>@enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="district" class="form-label">İlçe</label>
                                    <input type="text" class="form-control" id="district" name="district" value="{{ old('district', $branch->district) }}">
                                    @error('district')<div class="text-danger">{{ $message }}</div>@enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="phone" class="form-label">Telefon</label>
                                    <input type="text" class="form-control inputmask" id="phone" name="phone" value="{{ old('phone', $branch->phone) }}" placeholder="+90 5xx xxx xx xx">
                                    @error('phone')<div class="text-danger">{{ $message }}</div>@enderror
                                </div>
                            </div>
                        </div>
                        <div class="form-group mb-3">
                            <label for="email" class="form-label">E-posta</label>
                            <input type="email" class="form-control" id="email" name="email" value="{{ old('email', $branch->email) }}">
                            @error('email')<div class="text-danger">{{ $message }}</div>@enderror
                        </div>
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-success">Güncelle</button>
                            <a href="{{ route('customers.customer-branches.index', $customer->id) }}" class="btn btn-secondary">Geri Dön</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="{{ asset('assets/plugins/inputmask/jquery.inputmask.bundle.js') }}"></script>
<script>
    $(document).ready(function() {
        $(".inputmask").inputmask({
            mask: "+99 999 999 99 99",
            showMaskOnHover: false,
            showMaskOnFocus: true,
            clearIncomplete: true
        });
    });
</script>
@endpush 