<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\UserMigrationService;
use Exception;

class RestoreUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'restore:users 
                            {backup? : <PERSON><PERSON> yükle<PERSON>k yedek dosyası (opsiyonel)}
                            {--latest : En son yedekten geri yükle}
                            {--list : Mevcut yedekleri listele}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Users verilerini yedekten geri yükle';

    private UserMigrationService $migrationService;

    public function __construct(UserMigrationService $migrationService)
    {
        parent::__construct();
        $this->migrationService = $migrationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            if ($this->option('list')) {
                $this->listBackups();
                return 0;
            }

            $backupFile = $this->getBackupFile();
            
            if (!$backupFile) {
                $this->error('❌ <PERSON><PERSON> yükle<PERSON>cek yedek dosyası bulunamadı.');
                return 1;
            }

            $this->warn("⚠️  Bu işlem mevcut tüm user verilerini silecek!");
            $this->warn("📁 Geri yüklenecek dosya: {$backupFile}");
            
            if (!$this->confirm('Devam etmek istediğinizden emin misiniz?')) {
                $this->info('❌ İşlem iptal edildi.');
                return 0;
            }

            $this->info('🔄 Veriler geri yükleniyor...');
            $this->migrationService->restoreFromBackup($backupFile);
            $this->info('✅ Veriler başarıyla geri yüklendi!');

            // Doğrulama yap
            $this->info('🔍 Geri yükleme doğrulanıyor...');
            $this->showRestoreStats();

        } catch (Exception $e) {
            $this->error('❌ Hata: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * Geri yüklenecek yedek dosyasını belirle
     */
    private function getBackupFile(): ?string
    {
        // Komut satırından dosya belirtilmişse
        if ($this->argument('backup')) {
            $file = $this->argument('backup');
            // Tam yol değilse migrations/users klasörüne ekle
            if (!str_contains($file, '/')) {
                $file = "migrations/users/{$file}";
            }
            return $file;
        }

        // En son yedek seçeneği
        if ($this->option('latest')) {
            $backups = $this->migrationService->listBackups();
            return $backups[0]['file'] ?? null;
        }

        // İnteraktif seçim
        return $this->selectBackupInteractively();
    }

    /**
     * İnteraktif yedek seçimi
     */
    private function selectBackupInteractively(): ?string
    {
        $backups = $this->migrationService->listBackups();

        if (empty($backups)) {
            $this->warn('⚠️  Hiç yedek dosyası bulunamadı.');
            return null;
        }

        $this->info('📋 Mevcut yedek dosyaları:');
        
        $choices = [];
        foreach ($backups as $index => $backup) {
            $choice = sprintf(
                '%d. %s (%s - %d user)',
                $index + 1,
                $backup['timestamp'],
                $this->formatBytes($backup['size']),
                $backup['total_users']
            );
            $choices[] = $choice;
            $this->line($choice);
        }

        $selection = $this->ask('Hangi yedek dosyasını geri yüklemek istiyorsunuz? (1-' . count($backups) . ')');
        
        if (!is_numeric($selection) || $selection < 1 || $selection > count($backups)) {
            $this->error('❌ Geçersiz seçim.');
            return null;
        }

        return $backups[$selection - 1]['file'];
    }

    /**
     * Mevcut yedekleri listele
     */
    private function listBackups()
    {
        $backups = $this->migrationService->listBackups();

        if (empty($backups)) {
            $this->warn('⚠️  Hiç yedek dosyası bulunamadı.');
            return;
        }

        $this->info("📋 Toplam {" . count($backups) . "} yedek dosyası bulundu:");

        $tableData = [];
        foreach ($backups as $backup) {
            $tableData[] = [
                'Dosya' => basename($backup['file']),
                'Tarih' => $backup['timestamp'],
                'User Sayısı' => $backup['total_users'],
                'Boyut' => $this->formatBytes($backup['size']),
                'Oluşturulma' => date('d.m.Y H:i', $backup['created_at']),
            ];
        }

        $this->table(['Dosya', 'Tarih', 'User Sayısı', 'Boyut', 'Oluşturulma'], $tableData);
    }

    /**
     * Geri yükleme istatistiklerini göster
     */
    private function showRestoreStats()
    {
        $stats = $this->migrationService->getMigrationStats();
        
        $this->info('📊 Geri Yükleme İstatistikleri:');
        $this->table(['Bilgi', 'Değer'], [
            ['PostgreSQL Users', $stats['postgresql_users']],
            ['Mevcut Roles', $stats['available_roles']],
            ['Toplam Yedek Sayısı', $stats['available_backups']],
        ]);
    }

    /**
     * Byte'ları okunabilir formata çevir
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
