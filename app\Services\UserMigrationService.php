<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use App\Models\User;
use App\Models\Role;
use Exception;
use Carbon\Carbon;

class UserMigrationService
{
    private $backupPath = 'migrations/users';

    /**
     * MySQL'den users verilerini çek
     */
    public function fetchMySQLUsers(): array
    {
        try {
            return DB::connection('mysql')
                ->table('users')
                ->select('*')
                ->orderBy('id')
                ->get()
                ->toArray();
        } catch (Exception $e) {
            throw new Exception('MySQL\'den users verisi çekme hatası: ' . $e->getMessage());
        }
    }

    /**
     * Mevcut PostgreSQL users verilerini yedekle
     */
    public function backupCurrentUsers(): string
    {
        $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
        $backupFile = "{$this->backupPath}/users_backup_{$timestamp}.json";

        $currentUsers = User::with('role')->get()->toArray();
        
        $backupData = [
            'timestamp' => $timestamp,
            'total_users' => count($currentUsers),
            'users' => $currentUsers,
            'metadata' => [
                'php_version' => PHP_VERSION,
                'laravel_version' => app()->version(),
                'database_connection' => config('database.default'),
                'created_by' => 'UserMigrationService',
            ]
        ];

        Storage::put($backupFile, json_encode($backupData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        return $backupFile;
    }

    /**
     * Yedekten geri yükle
     */
    public function restoreFromBackup(string $backupFile): bool
    {
        if (!Storage::exists($backupFile)) {
            throw new Exception("Yedek dosyası bulunamadı: {$backupFile}");
        }

        $backupData = json_decode(Storage::get($backupFile), true);
        
        if (!$backupData || !isset($backupData['users'])) {
            throw new Exception('Geçersiz yedek dosyası formatı');
        }

        DB::beginTransaction();
        try {
            // Mevcut verileri sil
            User::truncate();

            // Yedekten verileri geri yükle
            foreach ($backupData['users'] as $userData) {
                User::create([
                    'name' => $userData['name'],
                    'email' => $userData['email'],
                    'phone' => $userData['phone'],
                    'password' => $userData['password'],
                    'role_id' => $userData['role_id'],
                    'email_verified_at' => $userData['email_verified_at'],
                    'remember_token' => $userData['remember_token'],
                    'created_at' => $userData['created_at'],
                    'updated_at' => $userData['updated_at'],
                ]);
            }

            DB::commit();
            return true;
        } catch (Exception $e) {
            DB::rollback();
            throw new Exception('Geri yükleme hatası: ' . $e->getMessage());
        }
    }

    /**
     * Mevcut yedek dosyalarını listele
     */
    public function listBackups(): array
    {
        $files = Storage::files($this->backupPath);
        $backups = [];

        foreach ($files as $file) {
            if (str_ends_with($file, '.json') && str_contains($file, 'users_backup_')) {
                $content = json_decode(Storage::get($file), true);
                $backups[] = [
                    'file' => $file,
                    'timestamp' => $content['timestamp'] ?? 'Unknown',
                    'total_users' => $content['total_users'] ?? 0,
                    'size' => Storage::size($file),
                    'created_at' => Storage::lastModified($file),
                ];
            }
        }

        // Tarihe göre sırala (en yeni önce)
        usort($backups, function($a, $b) {
            return $b['created_at'] <=> $a['created_at'];
        });

        return $backups;
    }

    /**
     * Eski yedek dosyalarını temizle
     */
    public function cleanOldBackups(int $keepCount = 5): int
    {
        $backups = $this->listBackups();
        $deletedCount = 0;

        if (count($backups) > $keepCount) {
            $toDelete = array_slice($backups, $keepCount);
            
            foreach ($toDelete as $backup) {
                Storage::delete($backup['file']);
                $deletedCount++;
            }
        }

        return $deletedCount;
    }

    /**
     * Migration istatistiklerini al
     */
    public function getMigrationStats(): array
    {
        try {
            $mysqlCount = DB::connection('mysql')->table('users')->count();
        } catch (Exception $e) {
            $mysqlCount = 'Bağlantı Hatası';
        }

        $postgresqlCount = User::count();
        $backupsCount = count($this->listBackups());
        $rolesCount = Role::count();

        return [
            'mysql_users' => $mysqlCount,
            'postgresql_users' => $postgresqlCount,
            'available_roles' => $rolesCount,
            'available_backups' => $backupsCount,
            'last_backup' => $this->getLastBackupInfo(),
        ];
    }

    /**
     * Son yedek bilgisini al
     */
    private function getLastBackupInfo(): ?array
    {
        $backups = $this->listBackups();
        return $backups[0] ?? null;
    }

    /**
     * Veri doğrulama
     */
    public function validateMigration(): array
    {
        $issues = [];

        try {
            $mysqlUsers = $this->fetchMySQLUsers();
            $postgresqlUsers = User::all();

            // Sayı kontrolü
            if (count($mysqlUsers) !== $postgresqlUsers->count()) {
                $issues[] = "Kayıt sayısı uyumsuzluğu: MySQL users({" . count($mysqlUsers) . "}) vs PostgreSQL users({$postgresqlUsers->count()})";
            }

            // Email kontrolü
            $mysqlEmails = array_column($mysqlUsers, 'email');
            $postgresqlEmails = $postgresqlUsers->pluck('email')->toArray();
            
            $missingInPostgres = array_diff($mysqlEmails, $postgresqlEmails);
            $extraInPostgres = array_diff($postgresqlEmails, $mysqlEmails);

            if (!empty($missingInPostgres)) {
                $issues[] = "PostgreSQL'de eksik email'ler: " . implode(', ', array_slice($missingInPostgres, 0, 5)) . (count($missingInPostgres) > 5 ? '...' : '');
            }

            if (!empty($extraInPostgres)) {
                $issues[] = "PostgreSQL'de fazla email'ler: " . implode(', ', array_slice($extraInPostgres, 0, 5)) . (count($extraInPostgres) > 5 ? '...' : '');
            }

            // Role kontrolü
            $usersWithoutRole = $postgresqlUsers->whereNull('role_id')->count();
            if ($usersWithoutRole > 0) {
                $issues[] = "Role'ü olmayan user sayısı: {$usersWithoutRole}";
            }

        } catch (Exception $e) {
            $issues[] = "Doğrulama hatası: " . $e->getMessage();
        }

        return $issues;
    }

    /**
     * İlk role ID'sini al
     */
    public function getFirstRoleId(): ?int
    {
        $firstRole = Role::first();
        return $firstRole ? $firstRole->id : null;
    }
}
