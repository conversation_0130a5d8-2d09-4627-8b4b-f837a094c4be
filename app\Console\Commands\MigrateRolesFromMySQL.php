<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\Role;
use App\Services\RoleMigrationService;
use Exception;

class MigrateRolesFromMySQL extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:roles-from-mysql 
                            {--dry-run : <PERSON><PERSON><PERSON> yap, veri kaydetme}
                            {--force : Mevcut verileri sil ve yeniden yükle}
                            {--chunk=100 : Kaç kayıt gruplarında işle}
                            {--backup : Migration öncesi yedek al}
                            {--validate : Migration sonrası doğrulama yap}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'MySQL user_role_groups tablosundan roles verilerini PostgreSQL\'e taşır';

    private RoleMigrationService $migrationService;

    public function __construct(RoleMigrationService $migrationService)
    {
        parent::__construct();
        $this->migrationService = $migrationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 MySQL user_role_groups\'den PostgreSQL roles\'e Migration Başlatılıyor...');
        
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');
        $chunkSize = (int) $this->option('chunk');
        $backup = $this->option('backup');
        $validate = $this->option('validate');

        try {
            // Migration istatistiklerini göster
            $this->showMigrationStats();

            // Yedek al
            if ($backup && !$dryRun) {
                $this->info('💾 Mevcut roles verileri yedekleniyor...');
                $backupFile = $this->migrationService->backupCurrentRoles();
                $this->info("✅ Yedek oluşturuldu: {$backupFile}");
            }

            // MySQL ve PostgreSQL bağlantılarını test et
            $this->info('📡 Bağlantılar test ediliyor...');
            $this->testConnections();

            // Mevcut verileri kontrol et
            $existingCount = Role::count();
            if ($existingCount > 0 && !$force) {
                $this->warn("⚠️  PostgreSQL'de {$existingCount} adet role kaydı bulundu.");
                if (!$this->confirm('Devam etmek istiyor musunuz? (Mevcut veriler korunacak)')) {
                    $this->info('❌ İşlem iptal edildi.');
                    return 0;
                }
            }

            // Force seçeneği ile mevcut verileri sil
            if ($force && $existingCount > 0) {
                $this->warn("🗑️  Force seçeneği aktif. {$existingCount} adet mevcut kayıt silinecek...");
                if (!$dryRun) {
                    Role::truncate();
                    $this->info('✅ Mevcut veriler silindi.');
                }
            }

            // MySQL'den verileri çek
            $this->info('📥 MySQL\'den user_role_groups verileri çekiliyor...');
            $mysqlUserRoleGrup = $this->migrationService->fetchMySQLUserRoleGrup();
            
            if (empty($mysqlUserRoleGrup)) {
                $this->warn('⚠️  MySQL\'de user_role_groups verisi bulunamadı.');
                return 0;
            }

            $this->info("📊 MySQL'de {" . count($mysqlUserRoleGrup) . "} adet user_role_groups bulundu.");

            // Verileri işle
            $this->processUserRoleGrup($mysqlUserRoleGrup, $dryRun, $chunkSize);

            // Doğrulama yap
            if ($validate && !$dryRun) {
                $this->info('🔍 Migration doğrulanıyor...');
                $issues = $this->migrationService->validateMigration();
                
                if (empty($issues)) {
                    $this->info('✅ Doğrulama başarılı. Tüm veriler doğru şekilde taşındı.');
                } else {
                    $this->warn('⚠️  Doğrulama sorunları:');
                    foreach ($issues as $issue) {
                        $this->warn("  - {$issue}");
                    }
                }
            }

            $this->info('🎉 Migration başarıyla tamamlandı!');
            
        } catch (Exception $e) {
            $this->error('❌ Hata: ' . $e->getMessage());
            $this->error('📍 Dosya: ' . $e->getFile() . ':' . $e->getLine());
            return 1;
        }

        return 0;
    }

    /**
     * Migration istatistiklerini göster
     */
    private function showMigrationStats()
    {
        $stats = $this->migrationService->getMigrationStats();
        
        $this->info('📊 Migration İstatistikleri:');
        $this->table(['Kaynak', 'Değer'], [
            ['MySQL user_role_groups', $stats['mysql_user_role_groups']],
            ['PostgreSQL roles', $stats['postgresql_roles']],
            ['Mevcut Yedekler', $stats['available_backups']],
            ['Son Yedek', $stats['last_backup']['timestamp'] ?? 'Yok'],
        ]);
    }

    /**
     * Bağlantıları test et
     */
    private function testConnections()
    {
        try {
            DB::connection('mysql')->getPdo();
            $this->info('✅ MySQL bağlantısı başarılı.');
        } catch (Exception $e) {
            throw new Exception('MySQL bağlantısı başarısız: ' . $e->getMessage());
        }

        try {
            DB::connection()->getPdo();
            $this->info('✅ PostgreSQL bağlantısı başarılı.');
        } catch (Exception $e) {
            throw new Exception('PostgreSQL bağlantısı başarısız: ' . $e->getMessage());
        }
    }

    /**
     * user_role_grup verilerini işle ve PostgreSQL'e roles olarak kaydet
     */
    private function processUserRoleGrup($mysqlUserRoleGrup, $dryRun, $chunkSize)
    {
        $chunks = array_chunk($mysqlUserRoleGrup, $chunkSize);
        $totalProcessed = 0;
        $totalErrors = 0;

        $progressBar = $this->output->createProgressBar(count($mysqlUserRoleGrup));
        $progressBar->start();

        foreach ($chunks as $chunk) {
            foreach ($chunk as $mysqlRole) {
                try {
                    $roleData = $this->mapUserRoleGrupToRole($mysqlRole);
                    
                    if ($dryRun) {
                        $this->line("\n🔍 Dry Run - İşlenecek veri:");
                        $this->table(['Alan', 'Değer'], [
                            ['ID (MySQL)', $mysqlRole->id ?? 'N/A'],
                            ['Name', $roleData['name']],
                            ['Display Name', $roleData['name']],
                            ['Description', $roleData['description'] ?? 'N/A'],
                        ]);
                    } else {
                        // Aynı isimde role var mı kontrol et
                        $existingRole = Role::where('name', $roleData['name'])->first();
                        
                        if ($existingRole) {
                            $this->warn("\n⚠️  '{$roleData['name']}' isimli role zaten mevcut. Atlanıyor...");
                        } else {
                            Role::create($roleData);
                            $totalProcessed++;
                        }
                    }
                    
                    $progressBar->advance();
                    
                } catch (Exception $e) {
                    $totalErrors++;
                    $this->error("\n❌ Hata (ID: {$mysqlRole->id}): " . $e->getMessage());
                    $progressBar->advance();
                }
            }
        }

        $progressBar->finish();
        $this->newLine(2);

        if ($dryRun) {
            $this->info("🔍 Dry Run tamamlandı. {" . count($mysqlUserRoleGrup) . "} kayıt işlenebilir durumda.");
        } else {
            $this->info("✅ {$totalProcessed} kayıt başarıyla işlendi.");
            if ($totalErrors > 0) {
                $this->warn("⚠️  {$totalErrors} kayıtta hata oluştu.");
            }
        }
    }

    /**
     * MySQL user_role_grup verisini PostgreSQL role formatına dönüştür
     */
    private function mapUserRoleGrupToRole($mysqlRole)
    {
        return [
            'name' => $mysqlRole->name ?? $mysqlRole->role_name ?? 'unknown_role',
            'display_name' => $mysqlRole->display_name ?? $mysqlRole->name ?? $mysqlRole->role_name ?? 'Bilinmeyen Rol',
            'description' => $mysqlRole->description ?? $mysqlRole->desc ?? null,
            'created_at' => $mysqlRole->created_at ?? now(),
            'updated_at' => $mysqlRole->updated_at ?? now(),
        ];
    }
}
