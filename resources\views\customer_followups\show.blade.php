@extends('layouts.index')

@section('content')
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card card-primary">
                <div class="card-header">
                    <h3 class="card-title text-center fw-bold py-2"><PERSON><PERSON><PERSON> Detayı - {{ $customer->company_name }}</h3>
                </div>
                <div class="card-body">
                    
                    <!-- <PERSON><PERSON><PERSON> Bilgileri -->
                    <h5 class="mb-3 text-primary"><PERSON><PERSON><PERSON> Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Takip Tarihi:</label>
                                <p class="mb-2">
                                    @if($followup->track_date)
                                        {{ \Carbon\Carbon::parse($followup->track_date)->format('d.m.Y') }}
                                    @else
                                        Belirtilmemiş
                                    @endif
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Görüşme Tarihi:</label>
                                <p class="mb-2">
                                    @if($followup->meet_date)
                                        {{ \Carbon\Carbon::parse($followup->meet_date)->format('d.m.Y') }}
                                    @else
                                        Belirtilmemiş
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Görüşme Türü:</label>
                                <p class="mb-2">
                                    @switch($followup->conversation_type)
                                        @case('telefon')
                                            <span class="badge bg-info">Telefon</span>
                                            @break
                                        @case('yerinde')
                                            <span class="badge bg-success">Yerinde</span>
                                            @break
                                        @case('bayide')
                                            <span class="badge bg-warning">Bayide</span>
                                            @break
                                        @default
                                            <span class="badge bg-secondary">{{ ucfirst($followup->conversation_type ?? 'Belirtilmemiş') }}</span>
                                    @endswitch
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Durum:</label>
                                <p class="mb-2">
                                    @switch($followup->status)
                                        @case('aktif')
                                            <span class="badge bg-success">Aktif</span>
                                            @break
                                        @case('pasif')
                                            <span class="badge bg-warning">Pasif</span>
                                            @break
                                        @case('eski müşteri')
                                            <span class="badge bg-secondary">Eski Müşteri</span>
                                            @break
                                        @case('hedef müşteri')
                                            <span class="badge bg-primary">Hedef Müşteri</span>
                                            @break
                                        @default
                                            <span class="badge bg-secondary">{{ ucfirst($followup->status ?? 'Belirtilmemiş') }}</span>
                                    @endswitch
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Görüşme Tarihindeki Durum:</label>
                                <p class="mb-2">
                                    @switch($followup->status_at_meeting_date)
                                        @case('aktif')
                                            <span class="badge bg-success">Aktif</span>
                                            @break
                                        @case('sektör değişikliği')
                                            <span class="badge bg-warning">Sektör Değişikliği</span>
                                            @break
                                        @case('taşınmış')
                                            <span class="badge bg-info">Taşınmış</span>
                                            @break
                                        @case('başka ekspertize gidiyor')
                                            <span class="badge bg-danger">Başka Ekspertize Gidiyor</span>
                                            @break
                                        @case('cari değişikliği')
                                            <span class="badge bg-primary">Cari Değişikliği</span>
                                            @break
                                        @case('birden fazla plus kart')
                                            <span class="badge bg-info">Birden Fazla Plus Kart</span>
                                            @break
                                        @case('diğer')
                                            <span class="badge bg-secondary">Diğer</span>
                                            @break
                                        @default
                                            <span class="badge bg-secondary">{{ $followup->status_at_meeting_date ?? 'Belirtilmemiş' }}</span>
                                    @endswitch
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Çalışma Türü:</label>
                                <p class="mb-2">
                                    @switch($followup->work_type)
                                        @case('PlusCard')
                                            <span class="badge bg-primary">PlusCard</span>
                                            @break
                                        @case('Kurumsal')
                                            <span class="badge bg-success">Kurumsal</span>
                                            @break
                                        @case('Filo')
                                            <span class="badge bg-warning">Filo</span>
                                            @break
                                        @default
                                            <span class="badge bg-secondary">{{ $followup->work_type ?? 'Belirtilmemiş' }}</span>
                                    @endswitch
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Ziyaret Eden Kişi:</label>
                                <p class="mb-2">{{ $followup->user_name ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Lokasyon Bilgileri -->
                    <h5 class="mb-3 text-primary">Lokasyon Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Şehir:</label>
                                <p class="mb-2">{{ $followup->city ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="fw-bold text-muted">İlçe:</label>
                                <p class="mb-2">{{ $followup->district ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Şube Adı:</label>
                                <p class="mb-2">{{ $followup->branch_name ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Firma Bilgileri -->
                    <h5 class="mb-3 text-primary">Firma Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Firma Adı:</label>
                                <p class="mb-2">{{ $followup->company_name ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Mevcut Firma:</label>
                                <p class="mb-2">{{ $followup->current_firm ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">E-posta:</label>
                                <p class="mb-2">{{ $followup->mail ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Görüşülen Kişi Bilgileri -->
                    <h5 class="mb-3 text-primary">Görüşülen Kişi Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Görüşülen Kişi Adı:</label>
                                <p class="mb-2">{{ $followup->contact_first_name ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Görüşülen Kişi Soyadı:</label>
                                <p class="mb-2">{{ $followup->contact_last_name ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Görüşülen Kişi Unvanı:</label>
                                <p class="mb-2">{{ $followup->contact_title ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Görüşülen Kişi Telefonu:</label>
                                <p class="mb-2">{{ $followup->contact_phone ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Görüşülen Kişi Emaili:</label>
                                <p class="mb-2">{{ $followup->contact_email ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Şube Bilgileri -->
                    <h5 class="mb-3 text-primary">Şube Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Mevcut Şube Sayısı:</label>
                                <p class="mb-2">{{ $followup->current_branch_count ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Şube Potansiyeli:</label>
                                <p class="mb-2">{{ $followup->branch_potential ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Anlaşma Bilgileri -->
                    <h5 class="mb-3 text-primary">Anlaşma Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Anlaşma Sağlandı mı?</label>
                                <p class="mb-2">
                                    @if($followup->agreement_status)
                                        <span class="badge bg-success">Evet</span>
                                    @else
                                        <span class="badge bg-danger">Hayır</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Pluscard Yükleme Yapıldı mı?</label>
                                <p class="mb-2">
                                    @if($followup->pluscard_been_loaded)
                                        <span class="badge bg-success">Evet</span>
                                    @else
                                        <span class="badge bg-danger">Hayır</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>

                    @if($followup->pluscard_been_loaded)
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Yükleme Yapılan Müşteri Sayısı:</label>
                                <p class="mb-2">{{ $followup->number_of_customers_loaded ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Yükleme Miktarı:</label>
                                <p class="mb-2">
                                    @if($followup->loading_amount)
                                        {{ number_format($followup->loading_amount, 2) }} ₺
                                    @else
                                        Belirtilmemiş
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>
                    @endif

                    @if(!$followup->pluscard_been_loaded)
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Anlaşamama Sebebi:</label>
                                <p class="mb-2">{{ $followup->reason_not_understanding ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Notlar -->
                    <h5 class="mb-3 text-primary">Notlar</h5>
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Not:</label>
                                <p class="mb-2">{{ $followup->note ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Açıklama:</label>
                                <p class="mb-2">{{ $followup->description ?? 'Belirtilmemiş' }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Sistem Bilgileri -->
                    <h5 class="mb-3 text-primary">Sistem Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Kayıt Tarihi:</label>
                                <p class="mb-2">{{ \Carbon\Carbon::parse($followup->created_at)->format('d.m.Y H:i') }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Son Güncelleme:</label>
                                <p class="mb-2">{{ \Carbon\Carbon::parse($followup->updated_at)->format('d.m.Y H:i') }}</p>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="card-footer d-flex gap-2">
                    <a href="{{ route('customers.customer-followups.edit', [$customer->id, $followup->id]) }}" class="btn btn-primary flex-fill">Düzenle</a>
                    <a href="{{ route('customers.customer-followups.index', $customer->id) }}" class="btn btn-secondary flex-fill">Geri Dön</a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.info-group {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #007bff;
    margin-bottom: 10px;
}

.info-group label {
    font-size: 0.9rem;
    margin-bottom: 5px;
    display: block;
}

.info-group p {
    font-size: 1rem;
    margin: 0;
    color: #333;
}

.badge {
    font-size: 0.8rem;
    padding: 6px 12px;
}
</style>
@endsection 