<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\Region;
use App\Services\RegionMigrationService;
use Exception;

class MigrateRegionsFromMySQL extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:regions-from-mysql
                            {--dry-run : <PERSON><PERSON>e ö<PERSON>zleme yap, veri kaydetme}
                            {--force : Mevcut verileri sil ve yeniden yükle}
                            {--chunk=100 : Kaç kayıt gruplarında işle}
                            {--backup : Migration öncesi yedek al}
                            {--validate : Migration sonrası doğrulama yap}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'MySQL veritabanından regions verilerini PostgreSQL\'e taşır';

    private RegionMigrationService $migrationService;

    public function __construct(RegionMigrationService $migrationService)
    {
        parent::__construct();
        $this->migrationService = $migrationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 MySQL\'den PostgreSQL\'e Regions Migration Başlatılıyor...');

        $dryRun = $this->option('dry-run');
        $force = $this->option('force');
        $chunkSize = (int) $this->option('chunk');
        $backup = $this->option('backup');
        $validate = $this->option('validate');

        try {
            // Migration istatistiklerini göster
            $this->showMigrationStats();

            // Yedek al
            if ($backup && !$dryRun) {
                $this->info('💾 Mevcut veriler yedekleniyor...');
                $backupFile = $this->migrationService->backupCurrentRegions();
                $this->info("✅ Yedek oluşturuldu: {$backupFile}");
            }
            // MySQL bağlantısını test et
            $this->info('📡 MySQL bağlantısı test ediliyor...');
            $this->testMySQLConnection();

            // PostgreSQL bağlantısını test et
            $this->info('📡 PostgreSQL bağlantısı test ediliyor...');
            $this->testPostgreSQLConnection();

            // Mevcut verileri kontrol et
            $existingCount = Region::count();
            if ($existingCount > 0 && !$force) {
                $this->warn("⚠️  PostgreSQL'de {$existingCount} adet region kaydı bulundu.");
                if (!$this->confirm('Devam etmek istiyor musunuz? (Mevcut veriler korunacak)')) {
                    $this->info('❌ İşlem iptal edildi.');
                    return 0;
                }
            }

            // Force seçeneği ile mevcut verileri sil
            if ($force && $existingCount > 0) {
                $this->warn("🗑️  Force seçeneği aktif. {$existingCount} adet mevcut kayıt silinecek...");
                if (!$dryRun) {
                    Region::truncate();
                    $this->info('✅ Mevcut veriler silindi.');
                }
            }

            // MySQL'den verileri çek
            $this->info('📥 MySQL\'den veriler çekiliyor...');
            $mysqlRegions = $this->migrationService->fetchMySQLRegions();

            if (empty($mysqlRegions)) {
                $this->warn('⚠️  MySQL\'de region verisi bulunamadı.');
                return 0;
            }

            $this->info("📊 MySQL'de {" . count($mysqlRegions) . "} adet region bulundu.");

            // Verileri işle
            $this->processRegions($mysqlRegions, $dryRun, $chunkSize);

            // Doğrulama yap
            if ($validate && !$dryRun) {
                $this->info('🔍 Migration doğrulanıyor...');
                $issues = $this->migrationService->validateMigration();

                if (empty($issues)) {
                    $this->info('✅ Doğrulama başarılı. Tüm veriler doğru şekilde taşındı.');
                } else {
                    $this->warn('⚠️  Doğrulama sorunları:');
                    foreach ($issues as $issue) {
                        $this->warn("  - {$issue}");
                    }
                }
            }

            $this->info('🎉 Migration başarıyla tamamlandı!');

        } catch (Exception $e) {
            $this->error('❌ Hata: ' . $e->getMessage());
            $this->error('📍 Dosya: ' . $e->getFile() . ':' . $e->getLine());
            return 1;
        }

        return 0;
    }

    /**
     * MySQL bağlantısını test et
     */
    private function testMySQLConnection()
    {
        try {
            DB::connection('mysql')->getPdo();
            $this->info('✅ MySQL bağlantısı başarılı.');
        } catch (Exception $e) {
            throw new Exception('MySQL bağlantısı başarısız: ' . $e->getMessage());
        }
    }

    /**
     * PostgreSQL bağlantısını test et
     */
    private function testPostgreSQLConnection()
    {
        try {
            DB::connection()->getPdo();
            $this->info('✅ PostgreSQL bağlantısı başarılı.');
        } catch (Exception $e) {
            throw new Exception('PostgreSQL bağlantısı başarısız: ' . $e->getMessage());
        }
    }

    /**
     * Migration istatistiklerini göster
     */
    private function showMigrationStats()
    {
        $stats = $this->migrationService->getMigrationStats();

        $this->info('📊 Migration İstatistikleri:');
        $this->table(['Kaynak', 'Değer'], [
            ['MySQL Regions', $stats['mysql_regions']],
            ['PostgreSQL Regions', $stats['postgresql_regions']],
            ['Mevcut Yedekler', $stats['available_backups']],
            ['Son Yedek', $stats['last_backup']['timestamp'] ?? 'Yok'],
        ]);
    }

    /**
     * Regions verilerini işle ve PostgreSQL'e kaydet
     */
    private function processRegions($mysqlRegions, $dryRun, $chunkSize)
    {
        $chunks = array_chunk($mysqlRegions, $chunkSize);
        $totalProcessed = 0;
        $totalErrors = 0;

        $progressBar = $this->output->createProgressBar(count($mysqlRegions));
        $progressBar->start();

        foreach ($chunks as $chunk) {
            foreach ($chunk as $mysqlRegion) {
                try {
                    $regionData = $this->mapRegionData($mysqlRegion);
                    
                    if ($dryRun) {
                        $this->line("\n🔍 Dry Run - İşlenecek veri:");
                        $this->table(['Alan', 'Değer'], [
                            ['ID (MySQL)', $mysqlRegion->id ?? 'N/A'],
                            ['Name', $regionData['name']],
                            ['Description', $regionData['description'] ?? 'N/A'],
                            ['Status', $regionData['status'] ? 'Aktif' : 'Pasif'],
                        ]);
                    } else {
                        // Aynı isimde region var mı kontrol et
                        $existingRegion = Region::where('name', $regionData['name'])->first();
                        
                        if ($existingRegion) {
                            $this->warn("\n⚠️  '{$regionData['name']}' isimli region zaten mevcut. Atlanıyor...");
                        } else {
                            Region::create($regionData);
                            $totalProcessed++;
                        }
                    }
                    
                    $progressBar->advance();
                    
                } catch (Exception $e) {
                    $totalErrors++;
                    $this->error("\n❌ Hata (ID: {$mysqlRegion->id}): " . $e->getMessage());
                    $progressBar->advance();
                }
            }
        }

        $progressBar->finish();
        $this->newLine(2);

        if ($dryRun) {
            $this->info("🔍 Dry Run tamamlandı. {" . count($mysqlRegions) . "} kayıt işlenebilir durumda.");
        } else {
            $this->info("✅ {$totalProcessed} kayıt başarıyla işlendi.");
            if ($totalErrors > 0) {
                $this->warn("⚠️  {$totalErrors} kayıtta hata oluştu.");
            }
        }
    }

    /**
     * MySQL region verisini PostgreSQL formatına dönüştür
     */
    private function mapRegionData($mysqlRegion)
    {
        return [
            'name' => $mysqlRegion->name ?? 'Bilinmeyen Bölge',
            'description' => null,
            'status' => isset($mysqlRegion->status) ? (bool) $mysqlRegion->status : true,
            'created_at' => $mysqlRegion->created_at ?? now(),
            'updated_at' => $mysqlRegion->updated_at ?? now(),
        ];
    }
}
