<!-- Navbar -->
<nav class="main-header navbar navbar-expand navbar-white navbar-light">
    <!-- Left navbar links -->
    <ul class="navbar-nav">
        <li class="nav-item">
            <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
        </li>
    </ul>

    <!-- Right navbar links -->
    <ul class="navbar-nav ml-auto">
        <!-- User Info -->
        @auth
        <li class="nav-item d-flex align-items-center">
            <span class="nav-link text-muted small mb-0">
                <span class="badge badge-primary mr-2">
                    @if(Auth::user()->role && is_object(Auth::user()->role))
                        {{ Auth::user()->role->display_name ?? 'Kullanıcı' }}
                    @elseif(Auth::user()->role && is_string(Auth::user()->role))
                        {{ Auth::user()->role }}
                    @else
                        <PERSON><PERSON><PERSON><PERSON><PERSON>
                    @endif
                </span>
                <span class="text-danger mr-2">#{{ Auth::user()->id }}</span>
                <span class="font-weight-bold">{{ Auth::user()->name }}</span>
            </span>
        </li>
        @endauth

        <li class="nav-item">
            <a class="nav-link" href="{{ route('logout') }}"
               onclick="event.preventDefault(); document.getElementById('logout-form').submit();"
               title="Çıkış Yap">
                <i class="fas fa-sign-out-alt"></i>
            </a>
            <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
                @csrf
            </form>
        </li>
    </ul>
</nav>
<!-- /.navbar -->
