@extends('layouts.index')

@section('content')
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Rol <PERSON>üzenle</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Ana Sayfa</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('role-management.index') }}">Rol Yönetimi</a></li>
                        <li class="breadcrumb-item active">Düzenle</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ $role->display_name }} - Düzenle</h3>
                </div>
                <form action="{{ route('role-management.update', $role) }}" method="POST">
                    @csrf
                    @method('PUT')
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name">Rol Adı (Sistem) <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name', $role->name) }}" required
                                           placeholder="Örn: sales_manager">
                                    <small class="form-text text-muted">
                                        Sadece küçük harf, rakam ve alt çizgi kullanın. Boşluk kullanmayın.
                                    </small>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="display_name">Görünen Ad <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('display_name') is-invalid @enderror" 
                                           id="display_name" name="display_name" value="{{ old('display_name', $role->display_name) }}" required
                                           placeholder="Örn: Satış Müdürü">
                                    <small class="form-text text-muted">
                                        Kullanıcı arayüzünde görünecek ad.
                                    </small>
                                    @error('display_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="description">Açıklama</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror" 
                                              id="description" name="description" rows="3"
                                              placeholder="Bu rolün sorumluluklarını ve yetkilerini açıklayın...">{{ old('description', $role->description) }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        @if($role->users()->count() > 0)
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="alert alert-warning">
                                        <h5><i class="icon fas fa-exclamation-triangle"></i> Dikkat!</h5>
                                        Bu rol <strong>{{ $role->users()->count() }}</strong> kullanıcı tarafından kullanılmaktadır. 
                                        Rol adını değiştirirken dikkatli olun.
                                    </div>
                                </div>
                            </div>
                        @endif

                        <div class="row">
                            <div class="col-md-12">
                                <div class="alert alert-info">
                                    <h5><i class="icon fas fa-info"></i> Bilgi:</h5>
                                    <ul class="mb-0">
                                        <li>Rol ID: <code>{{ $role->id }}</code></li>
                                        <li>Guard: <code>{{ $role->guard_name ?? 'web' }}</code></li>
                                        <li>Oluşturulma: {{ $role->created_at->format('d.m.Y H:i') }}</li>
                                        <li>Son Güncelleme: {{ $role->updated_at->format('d.m.Y H:i') }}</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Güncelle
                        </button>
                        <a href="{{ route('role-management.show', $role) }}" class="btn btn-info">
                            <i class="fas fa-eye"></i> Görüntüle
                        </a>
                        <a href="{{ route('role-management.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> İptal
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </section>
@endsection
