@extends('layouts.index')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2>{{ $customer->company_name }} - Taki<PERSON></h2>
        <a href="{{ route('customers.customer-followups.create', $customer->id) }}" class="btn btn-primary"><PERSON><PERSON></a>
    </div>
    @if(session('success'))
        <div class="alert alert-success">{{ session('success') }}</div>
    @endif

    <!-- Export Buttons -->
    <div class="mb-3 text-right">
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-success btn-sm" id="exportExcelFollowups">
                <i class="fas fa-file-excel"></i> Excel
            </button>
            <button type="button" class="btn btn-info btn-sm" onclick="window.print()">
                <i class="fas fa-print"></i> Yazdır
            </button>
        </div>
    </div>

    <table id="customerFollowupsTable" class="table table-bordered table-striped">
        <thead>
            <tr>
                <th>ID</th>
                <th>Takip Tarihi</th>
                <th>Durum</th>
                <th>Not</th>
                <th>Anlaşma</th>
                <th>İşlemler</th>
            </tr>
        </thead>
        <tbody>
            <!-- Server-side DataTable ile doldurulacak -->
        </tbody>
    </table>
    <a href="{{ route('customers.index') }}" class="btn btn-secondary mt-3">Müşteri Listesine Dön</a>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    var table = $('#customerFollowupsTable').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "{{ route('customers.customer-followups.datatable', $customer->id) }}",
            "type": "GET"
        },
        "language": {
            "url": "https://cdn.datatables.net/plug-ins/1.13.7/i18n/tr.json"
        },
        "pageLength": 25,
        "lengthMenu": [[10, 25, 50, 100], [10, 25, 50, 100]],
        "order": [[0, "desc"]],
        "responsive": true,
        "autoWidth": false,
        "columns": [
            { "data": 0, "name": "id" },
            { 
                "data": 1, 
                "name": "track_date",
                "render": function (data, type, row) {
                    if (!data) return '';
                    const date = new Date(data);
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    return `${year}-${month}-${day}`;
                }
            },
            { "data": 2, "name": "status" },
            { "data": 3, "name": "note" },
            { "data": 4, "name": "agreement_status" },
            { "data": 5, "name": "actions", "orderable": false, "searchable": false }
        ],
        "initComplete": function () {
            // Her sütun için filtreleme input'u ekle
            this.api().columns().every(function (index) {
                var column = this;
                var title = $(column.header()).text();

                // İşlemler sütunu için filtreleme ekleme
                if (index === $('#customerFollowupsTable thead th').length - 1) {
                    return;
                }

                // Header'a filtreleme input'u ekle
                var input = $('<input type="text" placeholder="' + title + ' ara..." class="form-control form-control-sm" style="margin-top: 5px;" />')
                    .appendTo($(column.header()))
                    .on('keyup change clear', function () {
                        if (column.search() !== this.value) {
                            column.search(this.value).draw();
                        }
                    });
            });
        }
    });

    // Export button handlers
    $('#exportExcelFollowups').on('click', function() {
        exportDataFollowups('excel');
    });

    function exportDataFollowups(type) {
        // Mevcut arama ve filtre parametrelerini al
        var searchValue = table.search();
        var columns = [];

        // Her sütun için filtre değerlerini al
        table.columns().every(function(index) {
            var column = this;
            var input = $(column.header()).find('input');
            var searchValue = input.length ? input.val() : '';
            columns.push({
                search: {
                    value: searchValue
                }
            });
        });

        // Form oluştur ve parametreleri ekle
        var form = $('<form>', {
            method: 'GET',
            action: type === 'excel' ? '{{ route("customers.customer-followups.export.excel", $customer->id) }}' : ''
        });

        // Genel arama parametresi
        if (searchValue) {
            form.append($('<input>', {
                type: 'hidden',
                name: 'search',
                value: searchValue
            }));
        }

        // Sütun filtre parametreleri
        columns.forEach(function(column, index) {
            if (column.search.value) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'columns[' + index + '][search][value]',
                    value: column.search.value
                }));
            }
        });

        // Formu body'ye ekle, submit et ve kaldır
        $('body').append(form);
        form.submit();
        form.remove();
    }
});
</script>
@endpush