<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use App\Models\Role;
use Exception;
use Carbon\Carbon;

class RoleMigrationService
{
    private $backupPath = 'migrations/roles';

    /**
     * MySQL'den user_role_groups verilerini çek
     */
    public function fetchMySQLUserRoleGrup(): array
    {
        try {
            return DB::connection('mysql')
                ->table('user_role_groups')
                ->select('*')
                ->orderBy('id')
                ->get()
                ->toArray();
        } catch (Exception $e) {
            throw new Exception('MySQL\'den user_role_groups verisi çekme hatası: ' . $e->getMessage());
        }
    }

    /**
     * Mevcut PostgreSQL roles verilerini yedekle
     */
    public function backupCurrentRoles(): string
    {
        $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
        $backupFile = "{$this->backupPath}/roles_backup_{$timestamp}.json";

        $currentRoles = Role::with('users')->get()->toArray();
        
        $backupData = [
            'timestamp' => $timestamp,
            'total_roles' => count($currentRoles),
            'roles' => $currentRoles,
            'metadata' => [
                'php_version' => PHP_VERSION,
                'laravel_version' => app()->version(),
                'database_connection' => config('database.default'),
                'created_by' => 'RoleMigrationService',
            ]
        ];

        Storage::put($backupFile, json_encode($backupData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        return $backupFile;
    }

    /**
     * Yedekten geri yükle
     */
    public function restoreFromBackup(string $backupFile): bool
    {
        if (!Storage::exists($backupFile)) {
            throw new Exception("Yedek dosyası bulunamadı: {$backupFile}");
        }

        $backupData = json_decode(Storage::get($backupFile), true);
        
        if (!$backupData || !isset($backupData['roles'])) {
            throw new Exception('Geçersiz yedek dosyası formatı');
        }

        DB::beginTransaction();
        try {
            // Mevcut verileri sil
            Role::truncate();

            // Yedekten verileri geri yükle
            foreach ($backupData['roles'] as $roleData) {
                Role::create([
                    'name' => $roleData['name'],
                    'display_name' => $roleData['display_name'],
                    'description' => $roleData['description'],
                    'created_at' => $roleData['created_at'],
                    'updated_at' => $roleData['updated_at'],
                ]);
            }

            DB::commit();
            return true;
        } catch (Exception $e) {
            DB::rollback();
            throw new Exception('Geri yükleme hatası: ' . $e->getMessage());
        }
    }

    /**
     * Mevcut yedek dosyalarını listele
     */
    public function listBackups(): array
    {
        $files = Storage::files($this->backupPath);
        $backups = [];

        foreach ($files as $file) {
            if (str_ends_with($file, '.json') && str_contains($file, 'roles_backup_')) {
                $content = json_decode(Storage::get($file), true);
                $backups[] = [
                    'file' => $file,
                    'timestamp' => $content['timestamp'] ?? 'Unknown',
                    'total_roles' => $content['total_roles'] ?? 0,
                    'size' => Storage::size($file),
                    'created_at' => Storage::lastModified($file),
                ];
            }
        }

        // Tarihe göre sırala (en yeni önce)
        usort($backups, function($a, $b) {
            return $b['created_at'] <=> $a['created_at'];
        });

        return $backups;
    }

    /**
     * Eski yedek dosyalarını temizle
     */
    public function cleanOldBackups(int $keepCount = 5): int
    {
        $backups = $this->listBackups();
        $deletedCount = 0;

        if (count($backups) > $keepCount) {
            $toDelete = array_slice($backups, $keepCount);
            
            foreach ($toDelete as $backup) {
                Storage::delete($backup['file']);
                $deletedCount++;
            }
        }

        return $deletedCount;
    }

    /**
     * Migration istatistiklerini al
     */
    public function getMigrationStats(): array
    {
        try {
            $mysqlCount = DB::connection('mysql')->table('user_role_groups')->count();
        } catch (Exception $e) {
            $mysqlCount = 'Bağlantı Hatası';
        }

        $postgresqlCount = Role::count();
        $backupsCount = count($this->listBackups());

        return [
            'mysql_user_role_groups' => $mysqlCount,
            'postgresql_roles' => $postgresqlCount,
            'available_backups' => $backupsCount,
            'last_backup' => $this->getLastBackupInfo(),
        ];
    }

    /**
     * Son yedek bilgisini al
     */
    private function getLastBackupInfo(): ?array
    {
        $backups = $this->listBackups();
        return $backups[0] ?? null;
    }

    /**
     * Veri doğrulama
     */
    public function validateMigration(): array
    {
        $issues = [];

        try {
            $mysqlUserRoleGroups = $this->fetchMySQLUserRoleGrup();
            $postgresqlRoles = Role::all();

            // Sayı kontrolü
            if (count($mysqlUserRoleGroups) !== $postgresqlRoles->count()) {
                $issues[] = "Kayıt sayısı uyumsuzluğu: MySQL user_role_groups({" . count($mysqlUserRoleGroups) . "}) vs PostgreSQL roles({$postgresqlRoles->count()})";
            }

            // İsim kontrolü
            $mysqlNames = array_column($mysqlUserRoleGroups, 'name');
            $postgresqlNames = $postgresqlRoles->pluck('name')->toArray();

            $missingInPostgres = array_diff($mysqlNames, $postgresqlNames);
            $extraInPostgres = array_diff($postgresqlNames, $mysqlNames);

            if (!empty($missingInPostgres)) {
                $issues[] = "PostgreSQL'de eksik: " . implode(', ', $missingInPostgres);
            }

            if (!empty($extraInPostgres)) {
                $issues[] = "PostgreSQL'de fazla: " . implode(', ', $extraInPostgres);
            }

        } catch (Exception $e) {
            $issues[] = "Doğrulama hatası: " . $e->getMessage();
        }

        return $issues;
    }
}
