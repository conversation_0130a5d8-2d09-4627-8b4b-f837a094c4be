<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use App\Models\Region;
use Exception;
use Carbon\Carbon;

class RegionMigrationService
{
    private $backupPath = 'migrations/regions';

    /**
     * MySQL'den regions verilerini çek
     */
    public function fetchMySQLRegions(): array
    {
        try {
            return DB::connection('mysql')
                ->table('zones')
                ->select('*')
                ->orderBy('id')
                ->get()
                ->toArray();
        } catch (Exception $e) {
            throw new Exception('MySQL\'den veri çekme hatası: ' . $e->getMessage());
        }
    }

    /**
     * Mevcut PostgreSQL regions verilerini yedekle
     */
    public function backupCurrentRegions(): string
    {
        $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
        $backupFile = "{$this->backupPath}/regions_backup_{$timestamp}.json";

        $currentRegions = Region::with('dealers')->get()->toArray();
        
        $backupData = [
            'timestamp' => $timestamp,
            'total_regions' => count($currentRegions),
            'regions' => $currentRegions,
            'metadata' => [
                'php_version' => PHP_VERSION,
                'laravel_version' => app()->version(),
                'database_connection' => config('database.default'),
                'created_by' => 'RegionMigrationService',
            ]
        ];

        Storage::put($backupFile, json_encode($backupData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        return $backupFile;
    }

    /**
     * Yedekten geri yükle
     */
    public function restoreFromBackup(string $backupFile): bool
    {
        if (!Storage::exists($backupFile)) {
            throw new Exception("Yedek dosyası bulunamadı: {$backupFile}");
        }

        $backupData = json_decode(Storage::get($backupFile), true);
        
        if (!$backupData || !isset($backupData['regions'])) {
            throw new Exception('Geçersiz yedek dosyası formatı');
        }

        DB::beginTransaction();
        try {
            // Mevcut verileri sil
            Region::truncate();

            // Yedekten verileri geri yükle
            foreach ($backupData['regions'] as $regionData) {
                $region = Region::create([
                    'name' => $regionData['name'],
                    'description' => $regionData['description'],
                    'status' => $regionData['status'],
                    'created_at' => $regionData['created_at'],
                    'updated_at' => $regionData['updated_at'],
                ]);

                // Dealers varsa onları da geri yükle (opsiyonel)
                if (isset($regionData['dealers']) && !empty($regionData['dealers'])) {
                    foreach ($regionData['dealers'] as $dealerData) {
                        $region->dealers()->create([
                            'name' => $dealerData['name'],
                            'contact_person' => $dealerData['contact_person'],
                            'phone' => $dealerData['phone'],
                            'email' => $dealerData['email'],
                            'address' => $dealerData['address'],
                            'city' => $dealerData['city'],
                            'district' => $dealerData['district'],
                            'status' => $dealerData['status'],
                            'created_at' => $dealerData['created_at'],
                            'updated_at' => $dealerData['updated_at'],
                        ]);
                    }
                }
            }

            DB::commit();
            return true;
        } catch (Exception $e) {
            DB::rollback();
            throw new Exception('Geri yükleme hatası: ' . $e->getMessage());
        }
    }

    /**
     * Mevcut yedek dosyalarını listele
     */
    public function listBackups(): array
    {
        $files = Storage::files($this->backupPath);
        $backups = [];

        foreach ($files as $file) {
            if (str_ends_with($file, '.json') && str_contains($file, 'regions_backup_')) {
                $content = json_decode(Storage::get($file), true);
                $backups[] = [
                    'file' => $file,
                    'timestamp' => $content['timestamp'] ?? 'Unknown',
                    'total_regions' => $content['total_regions'] ?? 0,
                    'size' => Storage::size($file),
                    'created_at' => Storage::lastModified($file),
                ];
            }
        }

        // Tarihe göre sırala (en yeni önce)
        usort($backups, function($a, $b) {
            return $b['created_at'] <=> $a['created_at'];
        });

        return $backups;
    }

    /**
     * Eski yedek dosyalarını temizle
     */
    public function cleanOldBackups(int $keepCount = 5): int
    {
        $backups = $this->listBackups();
        $deletedCount = 0;

        if (count($backups) > $keepCount) {
            $toDelete = array_slice($backups, $keepCount);
            
            foreach ($toDelete as $backup) {
                Storage::delete($backup['file']);
                $deletedCount++;
            }
        }

        return $deletedCount;
    }

    /**
     * Migration istatistiklerini al
     */
    public function getMigrationStats(): array
    {
        try {
            $mysqlCount = DB::connection('mysql')->table('regions')->count();
        } catch (Exception $e) {
            $mysqlCount = 'Bağlantı Hatası';
        }

        $postgresqlCount = Region::count();
        $backupsCount = count($this->listBackups());

        return [
            'mysql_regions' => $mysqlCount,
            'postgresql_regions' => $postgresqlCount,
            'available_backups' => $backupsCount,
            'last_backup' => $this->getLastBackupInfo(),
        ];
    }

    /**
     * Son yedek bilgisini al
     */
    private function getLastBackupInfo(): ?array
    {
        $backups = $this->listBackups();
        return $backups[0] ?? null;
    }

    /**
     * Veri doğrulama
     */
    public function validateMigration(): array
    {
        $issues = [];

        try {
            $mysqlRegions = $this->fetchMySQLRegions();
            $postgresqlRegions = Region::all();

            // Sayı kontrolü
            if (count($mysqlRegions) !== $postgresqlRegions->count()) {
                $issues[] = "Kayıt sayısı uyumsuzluğu: MySQL({" . count($mysqlRegions) . "}) vs PostgreSQL({$postgresqlRegions->count()})";
            }

            // İsim kontrolü
            $mysqlNames = array_column($mysqlRegions, 'name');
            $postgresqlNames = $postgresqlRegions->pluck('name')->toArray();
            
            $missingInPostgres = array_diff($mysqlNames, $postgresqlNames);
            $extraInPostgres = array_diff($postgresqlNames, $mysqlNames);

            if (!empty($missingInPostgres)) {
                $issues[] = "PostgreSQL'de eksik: " . implode(', ', $missingInPostgres);
            }

            if (!empty($extraInPostgres)) {
                $issues[] = "PostgreSQL'de fazla: " . implode(', ', $extraInPostgres);
            }

        } catch (Exception $e) {
            $issues[] = "Doğrulama hatası: " . $e->getMessage();
        }

        return $issues;
    }
}
