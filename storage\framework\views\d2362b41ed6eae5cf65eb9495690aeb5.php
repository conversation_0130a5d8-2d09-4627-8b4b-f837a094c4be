<?php $__env->startSection('content'); ?>
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card card-primary">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title"><?php echo e($customer->company_name); ?> - Şubeler</h3>
                </div>
                <div class="card-body">
                    <?php if(session('success')): ?>
                        <div class="alert alert-success"><?php echo e(session('success')); ?></div>
                    <?php endif; ?>
                    <a href="<?php echo e(route('customers.customer-branches.create', $customer->id)); ?>" class="btn btn-success btn-sm mb-4">+ Yeni <PERSON></a>

                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th><PERSON><PERSON> Adı</th>
                                <th>Adres</th>
                                <th>Şehir</th>
                                <th>İlçe</th>
                                <th>Telefon</th>
                                <th>E-posta</th>
                                <th>İşlemler</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($loop->iteration); ?></td>
                                    <td><?php echo e($branch->branch_name); ?></td>
                                    <td><?php echo e($branch->address); ?></td>
                                    <td><?php echo e($branch->city); ?></td>
                                    <td><?php echo e($branch->district); ?></td>
                                    <td><?php echo e($branch->phone); ?></td>
                                    <td><?php echo e($branch->email); ?></td>
                                    <td>
                                        <a href="<?php echo e(route('customers.customer-branches.edit', [$customer->id, $branch->id])); ?>" class="btn btn-warning btn-sm">Düzenle</a>
                                        <form action="<?php echo e(route('customers.customer-branches.destroy', [$customer->id, $branch->id])); ?>" method="POST" style="display:inline-block;">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Şubeyi silmek istediğinize emin misiniz?')">Sil</button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr><td colspan="8" class="text-center">Şube bulunamadı.</td></tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                    <a href="<?php echo e(route('customers.show', $customer->id)); ?>" class="btn btn-secondary mt-3">Müşteri Detayına Dön</a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.index', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/crm.umram.online/resources/views/customer_branches/index.blade.php ENDPATH**/ ?>