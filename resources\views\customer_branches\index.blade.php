@extends('layouts.index')

@section('content')
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card card-primary">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">{{ $customer->company_name }} - Şubeler</h3>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">{{ session('success') }}</div>
                    @endif
                    <a href="{{ route('customers.customer-branches.create', $customer->id) }}" class="btn btn-success btn-sm mb-4">+ <PERSON><PERSON></a>

                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Şube Adı</th>
                                <th>Adres</th>
                                <th>Şehir</th>
                                <th>İlçe</th>
                                <th>Telefon</th>
                                <th>E-posta</th>
                                <th><PERSON><PERSON><PERSON><PERSON></th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($branches as $branch)
                                <tr>
                                    <td>{{ $loop->iteration }}</td>
                                    <td>{{ $branch->branch_name }}</td>
                                    <td>{{ $branch->address }}</td>
                                    <td>{{ $branch->city }}</td>
                                    <td>{{ $branch->district }}</td>
                                    <td>{{ $branch->phone }}</td>
                                    <td>{{ $branch->email }}</td>
                                    <td>
                                        <a href="{{ route('customers.customer-branches.edit', [$customer->id, $branch->id]) }}" class="btn btn-warning btn-sm">Düzenle</a>
                                        <form action="{{ route('customers.customer-branches.destroy', [$customer->id, $branch->id]) }}" method="POST" style="display:inline-block;">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Şubeyi silmek istediğinize emin misiniz?')">Sil</button>
                                        </form>
                                    </td>
                                </tr>
                            @empty
                                <tr><td colspan="8" class="text-center">Şube bulunamadı.</td></tr>
                            @endforelse
                        </tbody>
                    </table>
                    <a href="{{ route('customers.show', $customer->id) }}" class="btn btn-secondary mt-3">Müşteri Detayına Dön</a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection 