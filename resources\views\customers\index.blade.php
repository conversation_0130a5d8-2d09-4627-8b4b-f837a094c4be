@extends('layouts.index')

@section('content')
<!-- Content Header (Page header) -->
<section class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1>Müşteri Listesi</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Ana Sayfa</a></li>
                    <li class="breadcrumb-item active">Müşteri Listesi</li>
                </ol>
            </div>
        </div>
    </div><!-- /.container-fluid -->
</section>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <a href="{{ route('customers.create') }}" class="btn btn-primary"><PERSON>ni <PERSON></a>
                        </h3>

                        <div class="card-tools">
                            @if(session('success'))
                                <div class="alert alert-success">
                                    {{ session('success') }}
                                </div>
                            @endif


                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body table-responsive p-0" style="height: 100%;">
                        <!-- Export Buttons -->
                        <div class="mb-3 text-right" style="padding: 15px;">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-success btn-sm" id="exportExcel">
                                    <i class="fas fa-file-excel"></i> Excel
                                </button>
                                <button type="button" class="btn btn-info btn-sm" onclick="window.print()">
                                    <i class="fas fa-print"></i> Yazdır
                                </button>
                            </div>
                        </div>

                        <table id="customersTable" class="table table-head-fixed text-nowrap table-bordered table-striped">
                            <thead>
                            <tr>
                                <th>ID</th>
                                <th>Email</th>
                                <th>Yetkili Unvan</th>
                                <th>Yetkili İsim</th>
                                <th>Yetkili Soyisim</th>
                                <th>Yetkili Telefon</th>
                                <th>Şirket İsmi</th>
                                <th>Şirket Telefonları</th>
                                <th>İl</th>
                                <th>İlçe</th>
                                <th>Kayıt Tarihi</th>
                                <th>İşlemler</th>
                            </tr>
                            </thead>
                            <tbody>
                                <!-- Server-side DataTable ile doldurulacak -->
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    var table = $('#customersTable').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "{{ route('customers.datatable') }}",
            "type": "GET"
        },
        "language": {
            "url": "https://cdn.datatables.net/plug-ins/1.13.7/i18n/tr.json"
        },
        "pageLength": 25,
        "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Tümü"]],
        "order": [[0, "desc"]],
        "responsive": true,
        "autoWidth": false,
        "columnDefs": [
            {
                "targets": -1, // Son sütun (İşlemler)
                "orderable": false,
                "searchable": false
            }
        ],
        "initComplete": function () {
            // Her sütun için filtreleme input'u ekle
            this.api().columns().every(function (index) {
                var column = this;
                var title = $(column.header()).text();

                // İşlemler sütunu için filtreleme ekleme
                if (index === $('#customersTable thead th').length - 1) {
                    return;
                }

                // Header'a filtreleme input'u ekle
                var input = $('<input type="text" placeholder="' + title + ' ara..." class="form-control form-control-sm" style="margin-top: 5px;" />')
                    .appendTo($(column.header()))
                    .on('keyup change clear', function () {
                        if (column.search() !== this.value) {
                            column.search(this.value).draw();
                        }
                    });
            });
        }
    });

    // Export button handlers
    $('#exportExcel').on('click', function() {
        exportData('excel');
    });

    function exportData(type) {
        // Mevcut arama ve filtre parametrelerini al
        var searchValue = table.search();
        var columns = [];

        // Her sütun için filtre değerlerini al
        table.columns().every(function(index) {
            var column = this;
            var input = $(column.header()).find('input');
            var searchValue = input.length ? input.val() : '';
            columns.push({
                search: {
                    value: searchValue
                }
            });
        });

        // Form oluştur ve parametreleri ekle
        var form = $('<form>', {
            method: 'GET',
            action: type === 'excel' ? '{{ route("customers.export.excel") }}' : ''
        });

        // Genel arama parametresi
        if (searchValue) {
            form.append($('<input>', {
                type: 'hidden',
                name: 'search',
                value: searchValue
            }));
        }

        // Sütun filtre parametreleri
        columns.forEach(function(column, index) {
            if (column.search.value) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'columns[' + index + '][search][value]',
                    value: column.search.value
                }));
            }
        });

        // Formu body'ye ekle, submit et ve kaldır
        $('body').append(form);
        form.submit();
        form.remove();
    }
});
</script>
@endpush
