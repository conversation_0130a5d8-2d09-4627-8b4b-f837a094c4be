@extends('layouts.index')

@section('title', '<PERSON>ölge <PERSON>')

@section('content')
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0"><PERSON><PERSON><PERSON> Detayı</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Ana Sayfa</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('regions.index') }}">B<PERSON>lge Yönetimi</a></li>
                    <li class="breadcrumb-item active">Bölge Detayı</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">{{ $region->name }}</h3>
                        <div class="card-tools">
                            @if($region->status)
                                <span class="badge badge-success">Aktif</span>
                            @else
                                <span class="badge badge-danger">Pasif</span>
                            @endif
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label>Bölge Adı:</label>
                                    <p>{{ $region->name }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label>Durum:</label>
                                    <p>
                                        @if($region->status)
                                            <span class="badge badge-success">Aktif</span>
                                        @else
                                            <span class="badge badge-danger">Pasif</span>
                                        @endif
                                    </p>
                                </div>
                            </div>
                        </div>

                        @if($region->description)
                        <div class="row">
                            <div class="col-12">
                                <div class="info-group">
                                    <label>Açıklama:</label>
                                    <p>{{ $region->description }}</p>
                                </div>
                            </div>
                        </div>
                        @endif

                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label>Oluşturulma Tarihi:</label>
                                    <p>{{ $region->created_at->format('d.m.Y H:i') }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label>Son Güncelleme:</label>
                                    <p>{{ $region->updated_at->format('d.m.Y H:i') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex gap-2">
                        <a href="{{ route('regions.edit', $region->id) }}" class="btn btn-primary flex-fill">Düzenle</a>
                        <a href="{{ route('regions.index') }}" class="btn btn-secondary flex-fill">Geri Dön</a>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Bayiler ({{ $region->dealers->count() }})</h3>
                    </div>
                    <div class="card-body">
                        @forelse($region->dealers as $dealer)
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <strong>{{ $dealer->name }}</strong><br>
                                    <small class="text-muted">{{ $dealer->city }} / {{ $dealer->district }}</small>
                                </div>
                                <div>
                                    @if($dealer->status)
                                        <span class="badge badge-success">Aktif</span>
                                    @else
                                        <span class="badge badge-danger">Pasif</span>
                                    @endif
                                </div>
                            </div>
                            <hr>
                        @empty
                            <p class="text-muted">Bu bölgeye henüz bayi eklenmemiş.</p>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.info-group {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #007bff;
    margin-bottom: 10px;
}

.info-group label {
    font-size: 0.9rem;
    margin-bottom: 5px;
    display: block;
}

.info-group p {
    font-size: 1rem;
    margin: 0;
    color: #333;
}

.badge {
    font-size: 0.8rem;
    padding: 6px 12px;
}
</style>
@endsection
