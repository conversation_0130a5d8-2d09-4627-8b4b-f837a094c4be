@extends('layouts.index')

@section('content')
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Kullanı<PERSON>ı Detayları</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Ana Sayfa</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('user-management.index') }}">Kullanıcı Yönetimi</a></li>
                        <li class="breadcrumb-item active">Detaylar</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ $user->name }}</h3>
                    <div class="card-tools">
                        <a href="{{ route('user-management.edit', $user) }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i> Düzenle
                        </a>
                        <a href="{{ route('user-management.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Geri
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="30%">ID:</th>
                                    <td>{{ $user->id }}</td>
                                </tr>
                                <tr>
                                    <th>Ad Soyad:</th>
                                    <td>{{ $user->name }}</td>
                                </tr>
                                <tr>
                                    <th>E-posta:</th>
                                    <td>{{ $user->email }}</td>
                                </tr>
                                <tr>
                                    <th>Telefon:</th>
                                    <td>{{ $user->phone }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="30%">Rol:</th>
                                    <td>
                                        @if($user->role)
                                            <span class="badge badge-info">{{ $user->role->display_name }}</span>
                                        @else
                                            <span class="badge badge-secondary">Rol Atanmamış</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th>E-posta Doğrulandı:</th>
                                    <td>
                                        @if($user->email_verified_at)
                                            <span class="badge badge-success">
                                                <i class="fas fa-check"></i> {{ $user->email_verified_at->format('d.m.Y H:i') }}
                                            </span>
                                        @else
                                            <span class="badge badge-warning">
                                                <i class="fas fa-times"></i> Doğrulanmamış
                                            </span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th>Oluşturulma Tarihi:</th>
                                    <td>{{ $user->created_at->format('d.m.Y H:i') }}</td>
                                </tr>
                                <tr>
                                    <th>Son Güncelleme:</th>
                                    <td>{{ $user->updated_at->format('d.m.Y H:i') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    @if($user->role)
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5>Rol Bilgileri</h5>
                                <div class="card card-outline card-info">
                                    <div class="card-body">
                                        <table class="table table-borderless">
                                            <tr>
                                                <th width="20%">Rol Adı:</th>
                                                <td>{{ $user->role->display_name }}</td>
                                            </tr>
                                            <tr>
                                                <th>Sistem Adı:</th>
                                                <td><code>{{ $user->role->name }}</code></td>
                                            </tr>
                                            @if($user->role->description)
                                                <tr>
                                                    <th>Açıklama:</th>
                                                    <td>{{ $user->role->description }}</td>
                                                </tr>
                                            @endif
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </section>
@endsection
