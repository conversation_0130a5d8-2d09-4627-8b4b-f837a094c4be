<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\PotentialCustomer;
use App\Models\PotentialCustomerPhone;
use Illuminate\Http\Response;

class PotentialCustomerController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('potential_customers.index');
    }

    public function datatable()
    {
        $query = PotentialCustomer::with(['authorizedPersons', 'phones']);

        // DataTables parametrelerini al
        $draw = request('draw');
        $start = request('start', 0);
        $length = request('length', 10);
        $searchValue = request('search.value');
        $orderColumn = request('order.0.column', 0);
        $orderDir = request('order.0.dir', 'desc');

        // Sütun isimleri
        $columns = ['id', 'company_name', 'authorized_name', 'authorized_lastname', 'phones', 'city', 'district', 'offer_status', 'offer_date'];

        // Genel arama
        if ($searchValue) {
            $query->where(function($q) use ($searchValue) {
                $q->where('company_name', 'ilike', "%{$searchValue}%")
                  ->orWhere('city', 'ilike', "%{$searchValue}%")
                  ->orWhere('district', 'ilike', "%{$searchValue}%")
                  ->orWhere('offer_status', 'ilike', "%{$searchValue}%")
                  ->orWhereHas('authorizedPersons', function($q) use ($searchValue) {
                      $q->where('name', 'ilike', "%{$searchValue}%")
                        ->orWhere('lastname', 'ilike', "%{$searchValue}%");
                  });
            });
        }

        // Sütun bazlı arama
        for ($i = 0; $i < count($columns); $i++) {
            $columnSearch = request("columns.{$i}.search.value");
            if ($columnSearch && isset($columns[$i])) {
                $column = $columns[$i];
                if ($column === 'authorized_name') {
                    $query->where(function($q) use ($columnSearch) {
                        $q->where('authorized_name', 'ilike', "%{$columnSearch}%")
                          ->orWhereHas('authorizedPersons', function($subQ) use ($columnSearch) {
                              $subQ->where('name', 'ilike', "%{$columnSearch}%");
                          });
                    });
                } elseif ($column === 'authorized_lastname') {
                    $query->where(function($q) use ($columnSearch) {
                        $q->where('authorized_lastname', 'ilike', "%{$columnSearch}%")
                          ->orWhereHas('authorizedPersons', function($subQ) use ($columnSearch) {
                              $subQ->where('lastname', 'ilike', "%{$columnSearch}%");
                          });
                    });
                } elseif ($column !== 'phones') {
                    $query->where($column, 'ilike', "%{$columnSearch}%");
                }
            }
        }

        // Toplam kayıt sayısı
        $totalRecords = PotentialCustomer::count();
        $filteredRecords = $query->count();

        // Sıralama
        if (isset($columns[$orderColumn])) {
            $column = $columns[$orderColumn];
            if ($column === 'authorized_name' || $column === 'authorized_lastname') {
                // Önce kendi alanına göre sırala, sonra authorizedPersons'a göre
                $query->orderBy($column, $orderDir);
            } else {
                $query->orderBy($column, $orderDir);
            }
        }

        // Sayfalama
        $potentialCustomers = $query->skip($start)->take($length)->get();

        // Veriyi formatla
        $data = [];
        foreach ($potentialCustomers as $customer) {
            // Telefon bilgilerini topla
            $allPhones = collect();
            if($customer->phones && $customer->phones->count() > 0) {
                foreach($customer->phones as $phone) {
                    $allPhones->push($phone->phone . ' (' . $phone->type . ')');
                }
            }
            $phonesText = $allPhones->count() > 0 ? $allPhones->map(function($phone, $index) {
                return '<small class="d-block"><span class="badge badge-primary me-1">' . ($index + 1) . '</span>' . $phone . '</small>';
            })->implode('') : '<small class="text-muted">Telefon yok</small>';

            // Yetkili isim ve soyisim için hem eski sistem hem yeni sistem verilerini kontrol et
            $authorizedName = '-';
            $authorizedLastname = '-';

            // Önce eski sistemdeki verileri kontrol et
            if ($customer->authorized_name) {
                $authorizedName = $customer->authorized_name;
            }
            if ($customer->authorized_lastname) {
                $authorizedLastname = $customer->authorized_lastname;
            }

            // Sonra yeni sistemdeki verileri kontrol et (varsa üzerine yaz)
            if ($customer->authorizedPersons && $customer->authorizedPersons->count() > 0) {
                $firstPerson = $customer->authorizedPersons->first();
                if ($firstPerson->name) {
                    $authorizedName = $firstPerson->name;
                }
                if ($firstPerson->lastname) {
                    $authorizedLastname = $firstPerson->lastname;
                }
            }

            $data[] = [
                $customer->id,
                $customer->company_name ?? '-',
                $authorizedName,
                $authorizedLastname,
                $phonesText,
                $customer->city ?? '-',
                $customer->district ?? '-',
                ucfirst($customer->offer_status ?? '-'),
                $customer->offer_date ?? '-',
                '<a href="' . route('potential-customers.show', $customer->id) . '" class="btn btn-info btn-sm">Detay</a> ' .
                '<a href="' . route('potential-customers.edit', $customer->id) . '" class="btn btn-warning btn-sm">Düzenle</a>'
            ];
        }

        return response()->json([
            'draw' => intval($draw),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('potential_customers.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'company_name' => 'required|string|max:255',
            'authorized_persons' => 'required|array|min:1',
            'authorized_persons.*.title' => 'nullable|string|max:255',
            'authorized_persons.*.name' => 'nullable|string|max:255',
            'authorized_persons.*.lastname' => 'nullable|string|max:255',
            'authorized_persons.*.phone' => 'nullable|string|max:20',
            'company_phones' => 'nullable|array',
            'company_phones.*.phone' => 'nullable|string|max:20',
            'company_phones.*.type' => 'nullable|string|max:20',
            'city' => 'required|string|max:100',
            'district' => 'required|string|max:100',
            'address' => 'required|string',
            'offer_status' => PotentialCustomer::getOfferStatusValidationRule(),
            'offer_date' => 'nullable|date',
            'is_working_with_us' => 'required|boolean',
        ]);

        $authorizedPersons = $validated['authorized_persons'] ?? [];
        $companyPhones = $validated['company_phones'] ?? [];
        unset($validated['authorized_persons'], $validated['company_phones']);

        $potentialCustomer = PotentialCustomer::create($validated);

        // Yetkili kişileri ekle
        foreach ($authorizedPersons as $person) {
            if (!empty(trim($person['name'] ?? '')) ||
                !empty(trim($person['lastname'] ?? '')) ||
                !empty(trim($person['title'] ?? '')) ||
                !empty(trim($person['phone'] ?? ''))) {

                $potentialCustomer->authorizedPersons()->create([
                    'title' => trim($person['title'] ?? ''),
                    'name' => trim($person['name'] ?? ''),
                    'lastname' => trim($person['lastname'] ?? ''),
                    'phone' => trim($person['phone'] ?? ''),
                ]);
            }
        }

        // Şirket telefonlarını ekle
        foreach ($companyPhones as $phone) {
            if (!empty(trim($phone['phone'] ?? ''))) {
                $potentialCustomer->phones()->create([
                    'phone' => trim($phone['phone']),
                    'type' => trim($phone['type'] ?? 'Sabit'),
                ]);
            }
        }

        return redirect()->route('potential-customers.index')->with('success', 'Potansiyel müşteri başarıyla eklendi!');
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $potentialCustomer = PotentialCustomer::with(['authorizedPersons', 'phones'])->findOrFail($id);
        return view('potential_customers.show', compact('potentialCustomer'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        $potentialCustomer = PotentialCustomer::with(['authorizedPersons', 'phones'])->findOrFail($id);
        return view('potential_customers.edit', compact('potentialCustomer'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, PotentialCustomer $potentialCustomer)
    {
        $validated = $request->validate([
            'company_name' => 'required|string|max:255',
            'authorized_persons' => 'required|array|min:1',
            'authorized_persons.*.title' => 'nullable|string|max:255',
            'authorized_persons.*.name' => 'nullable|string|max:255',
            'authorized_persons.*.lastname' => 'nullable|string|max:255',
            'authorized_persons.*.phone' => 'nullable|string|max:20',
            'company_phones' => 'nullable|array',
            'company_phones.*.phone' => 'nullable|string|max:20',
            'company_phones.*.type' => 'nullable|string|max:20',
            'company_phones.*.id' => 'nullable|integer',
            'city' => 'required|string|max:100',
            'district' => 'required|string|max:100',
            'address' => 'required|string',
            'offer_status' => PotentialCustomer::getOfferStatusValidationRule(),
            'offer_date' => 'nullable|date',
            'is_working_with_us' => 'required|boolean',
        ]);

        $authorizedPersons = $validated['authorized_persons'] ?? [];
        $companyPhones = $validated['company_phones'] ?? [];
        unset($validated['authorized_persons'], $validated['company_phones']);

        $potentialCustomer->update($validated);

        // Yetkili kişileri güncelle
        $potentialCustomer->authorizedPersons()->delete();

        foreach ($authorizedPersons as $person) {
            if (!empty(trim($person['name'] ?? '')) ||
                !empty(trim($person['lastname'] ?? '')) ||
                !empty(trim($person['title'] ?? '')) ||
                !empty(trim($person['phone'] ?? ''))) {

                $potentialCustomer->authorizedPersons()->create([
                    'title' => trim($person['title'] ?? ''),
                    'name' => trim($person['name'] ?? ''),
                    'lastname' => trim($person['lastname'] ?? ''),
                    'phone' => trim($person['phone'] ?? ''),
                ]);
            }
        }

        // Şirket telefonlarını güncelle
        $existingPhoneIds = [];
        foreach ($companyPhones as $phone) {
            if (!empty(trim($phone['phone'] ?? ''))) {
                if (!empty($phone['id'])) {
                    // Mevcut telefonu güncelle
                    $existingPhone = $potentialCustomer->phones()->find($phone['id']);
                    if ($existingPhone) {
                        $existingPhone->update([
                            'phone' => trim($phone['phone']),
                            'type' => trim($phone['type'] ?? 'Sabit'),
                        ]);
                        $existingPhoneIds[] = $phone['id'];
                    }
                } else {
                    // Yeni telefon ekle
                    $newPhone = $potentialCustomer->phones()->create([
                        'phone' => trim($phone['phone']),
                        'type' => trim($phone['type'] ?? 'Sabit'),
                    ]);
                    $existingPhoneIds[] = $newPhone->id;
                }
            }
        }

        // Silinmiş telefonları kaldır
        $potentialCustomer->phones()->whereNotIn('id', $existingPhoneIds)->delete();

        return redirect()->route('potential-customers.index')->with('success', 'Potansiyel müşteri başarıyla güncellendi!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $potentialCustomer = PotentialCustomer::findOrFail($id);
        $potentialCustomer->delete();
        return redirect()->route('potential-customers.index')->with('success', 'Potansiyel müşteri silindi!');
    }

    public function exportExcel(Request $request)
    {
        $query = PotentialCustomer::with(['phones', 'authorizedPersons']);

        // Filtre parametrelerini al
        $searchValue = $request->get('search');

        // Genel arama
        if ($searchValue) {
            $query->where(function($q) use ($searchValue) {
                $q->where('company_name', 'ilike', "%{$searchValue}%")
                  ->orWhere('city', 'ilike', "%{$searchValue}%")
                  ->orWhere('district', 'ilike', "%{$searchValue}%")
                  ->orWhere('offer_status', 'ilike', "%{$searchValue}%")
                  ->orWhereHas('authorizedPersons', function($subQ) use ($searchValue) {
                      $subQ->where('name', 'ilike', "%{$searchValue}%")
                           ->orWhere('lastname', 'ilike', "%{$searchValue}%");
                  });
            });
        }

        // Sütun bazlı filtreler
        if ($request->has('columns')) {
            foreach ($request->get('columns') as $index => $column) {
                if (!empty($column['search']['value'])) {
                    $searchTerm = $column['search']['value'];
                    switch ($index) {
                        case 0: // ID
                            $query->where('id', 'like', "%{$searchTerm}%");
                            break;
                        case 1: // Company Name
                            $query->where('company_name', 'ilike', "%{$searchTerm}%");
                            break;
                        case 2: // Authorized Name
                            $query->whereHas('authorizedPersons', function($subQ) use ($searchTerm) {
                                $subQ->where('name', 'ilike', "%{$searchTerm}%");
                            });
                            break;
                        case 3: // Authorized Lastname
                            $query->whereHas('authorizedPersons', function($subQ) use ($searchTerm) {
                                $subQ->where('lastname', 'ilike', "%{$searchTerm}%");
                            });
                            break;
                        case 5: // City
                            $query->where('city', 'ilike', "%{$searchTerm}%");
                            break;
                        case 6: // District
                            $query->where('district', 'ilike', "%{$searchTerm}%");
                            break;
                        case 7: // Offer Status
                            $query->where('offer_status', 'ilike', "%{$searchTerm}%");
                            break;
                        case 8: // Offer Date
                            $query->where('offer_date', 'like', "%{$searchTerm}%");
                            break;
                    }
                }
            }
        }

        $customers = $query->get();

        $filename = 'potansiyel_musteriler_' . date('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($customers) {
            $file = fopen('php://output', 'w');

            // UTF-8 BOM ekle
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            // Header satırı
            fputcsv($file, [
                'ID',
                'Firma Adı',
                'Yetkili İsim',
                'Yetkili Soyisim',
                'Şirket Telefonları',
                'Şehir',
                'İlçe',
                'Teklif Durumu',
                'Teklif Tarihi'
            ]);

            // Veri satırları
            foreach ($customers as $customer) {
                $phones = $customer->phones->pluck('phone')->implode(', ');
                $authorizedName = $customer->authorizedPersons->first()->name ?? '-';
                $authorizedLastname = $customer->authorizedPersons->first()->lastname ?? '-';

                fputcsv($file, [
                    $customer->id,
                    $customer->company_name ?? '-',
                    $authorizedName,
                    $authorizedLastname,
                    $phones ?: '-',
                    $customer->city ?? '-',
                    $customer->district ?? '-',
                    ucfirst($customer->offer_status ?? '-'),
                    $customer->offer_date ?? '-'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

}









