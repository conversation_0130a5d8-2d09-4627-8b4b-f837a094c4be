<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Console\Commands\MigrateRegionsFromMySQL;
use App\Console\Commands\BackupRegions;
use App\Console\Commands\RestoreRegions;
use App\Console\Commands\MigrateDealersFromMySQL;
use App\Console\Commands\BackupDealers;
use App\Console\Commands\RestoreDealers;
use App\Console\Commands\MigrateRolesFromMySQL;
use App\Console\Commands\BackupRoles;
use App\Console\Commands\RestoreRoles;
use App\Console\Commands\MigrateUsersFromMySQL;
use App\Console\Commands\BackupUsers;
use App\Console\Commands\RestoreUsers;

class MigrationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        if ($this->app->runningInConsole()) {
            $this->commands([
                MigrateRegionsFromMySQL::class,
                BackupRegions::class,
                RestoreRegions::class,
                MigrateDealersFromMySQL::class,
                BackupDealers::class,
                RestoreDealers::class,
                MigrateRolesFromMySQL::class,
                BackupRoles::class,
                RestoreRoles::class,
                MigrateUsersFromMySQL::class,
                BackupUsers::class,
                RestoreUsers::class,
            ]);
        }
    }
}
